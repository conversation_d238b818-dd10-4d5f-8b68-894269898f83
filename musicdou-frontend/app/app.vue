<template>
  <div :class="$colorMode.value">
    <NuxtRouteAnnouncer />
    <NuxtLayout>
      <NuxtPage />
    </NuxtLayout>
    <!-- 全局通知容器 -->
    <UiNotificationContainer />
  </div>
</template>

<script setup lang="ts">
// 页面元数据
useHead({
  title: 'MusicDou - 现代化音乐分享平台',
  meta: [
    { name: 'description', content: '发现、分享和享受音乐的最佳平台' },
    { name: 'viewport', content: 'width=device-width, initial-scale=1' }
  ],
  link: [
    { rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' }
  ]
})
</script>
