import type { Playlist, PlaylistForm, Music, ApiResponse, PaginatedResponse } from '~/types'

export const usePlaylistApi = () => {
  const { get, post, put, delete: del } = useApi()

  // 获取歌单列表
  const getPlaylists = async (params?: {
    page?: number
    limit?: number
    userId?: string
    isPublic?: boolean
    sortBy?: 'latest' | 'popular' | 'name'
    search?: string
  }): Promise<ApiResponse<PaginatedResponse<Playlist>>> => {
    return await get('/playlists', params)
  }

  // 获取歌单详情
  const getPlaylistById = async (id: string): Promise<ApiResponse<Playlist>> => {
    return await get(`/playlists/${id}`)
  }

  // 创建歌单
  const createPlaylist = async (playlistData: PlaylistForm): Promise<ApiResponse<Playlist>> => {
    return await post('/playlists', playlistData)
  }

  // 更新歌单
  const updatePlaylist = async (id: string, playlistData: Partial<PlaylistForm>): Promise<ApiResponse<Playlist>> => {
    return await put(`/playlists/${id}`, playlistData)
  }

  // 删除歌单
  const deletePlaylist = async (id: string): Promise<ApiResponse<{ message: string }>> => {
    return await del(`/playlists/${id}`)
  }

  // 获取歌单中的歌曲
  const getPlaylistSongs = async (id: string, params?: {
    page?: number
    limit?: number
  }): Promise<ApiResponse<PaginatedResponse<Music>>> => {
    return await get(`/playlists/${id}/songs`, params)
  }

  // 添加歌曲到歌单
  const addSongToPlaylist = async (playlistId: string, songId: string): Promise<ApiResponse<{ message: string }>> => {
    return await post(`/playlists/${playlistId}/songs`, { songId })
  }

  // 从歌单移除歌曲
  const removeSongFromPlaylist = async (playlistId: string, songId: string): Promise<ApiResponse<{ message: string }>> => {
    return await del(`/playlists/${playlistId}/songs/${songId}`)
  }

  // 批量添加歌曲到歌单
  const addSongsToPlaylist = async (playlistId: string, songIds: string[]): Promise<ApiResponse<{ added: number }>> => {
    return await post(`/playlists/${playlistId}/songs/batch`, { songIds })
  }

  // 批量移除歌单中的歌曲
  const removeSongsFromPlaylist = async (playlistId: string, songIds: string[]): Promise<ApiResponse<{ removed: number }>> => {
    // 使用 POST 请求进行批量删除，因为需要传递 songIds 数组
    return await post(`/playlists/${playlistId}/songs/batch-remove`, { songIds })
  }

  // 调整歌单中歌曲顺序
  const reorderPlaylistSongs = async (playlistId: string, songOrders: Array<{ songId: string; position: number }>): Promise<ApiResponse<{ message: string }>> => {
    return await put(`/playlists/${playlistId}/songs/reorder`, { songOrders })
  }

  // 点赞歌单
  const likePlaylist = async (id: string): Promise<ApiResponse<{ isLiked: boolean; likeCount: number }>> => {
    return await post(`/playlists/${id}/like`)
  }

  // 取消点赞歌单
  const unlikePlaylist = async (id: string): Promise<ApiResponse<{ isLiked: boolean; likeCount: number }>> => {
    return await del(`/playlists/${id}/like`)
  }

  // 分享歌单
  const sharePlaylist = async (id: string, platform?: string): Promise<ApiResponse<{ shareCount: number; shareUrl: string }>> => {
    return await post(`/playlists/${id}/share`, { platform })
  }

  // 复制歌单
  const copyPlaylist = async (id: string, newName?: string): Promise<ApiResponse<Playlist>> => {
    return await post(`/playlists/${id}/copy`, { name: newName })
  }

  // 获取用户的歌单
  const getUserPlaylists = async (userId: string, params?: {
    page?: number
    limit?: number
    includePrivate?: boolean
  }): Promise<ApiResponse<PaginatedResponse<Playlist>>> => {
    return await get(`/playlists/user/${userId}`, params)
  }

  // 获取用户喜欢的歌单
  const getUserLikedPlaylists = async (params?: {
    page?: number
    limit?: number
  }): Promise<ApiResponse<PaginatedResponse<Playlist>>> => {
    return await get('/playlists/favorites', params)
  }

  // 搜索歌单
  const searchPlaylists = async (params: {
    q: string
    page?: number
    limit?: number
    isPublic?: boolean
    sortBy?: 'relevance' | 'latest' | 'popular'
  }): Promise<ApiResponse<PaginatedResponse<Playlist>>> => {
    return await get('/playlists/search', params)
  }

  // 获取推荐歌单
  const getRecommendedPlaylists = async (params?: {
    limit?: number
    type?: 'similar' | 'genre' | 'collaborative'
  }): Promise<ApiResponse<Playlist[]>> => {
    try {
      // 使用热门歌单作为推荐歌单
      const response = await get('/playlists/popular', {
        limit: params?.limit || 8,
        page: 1
      })

      // 转换响应格式以匹配预期的数组格式
      if (response.success && response.data?.playlists) {
        return {
          ...response,
          data: response.data.playlists
        }
      }

      return response
    } catch (error) {
      console.error('获取推荐歌单失败:', error)
      throw error
    }
  }

  // 获取热门歌单
  const getTrendingPlaylists = async (params?: {
    period?: 'day' | 'week' | 'month'
    limit?: number
  }): Promise<ApiResponse<Playlist[]>> => {
    return await get('/playlists/trending', params)
  }

  // 上传歌单封面
  const uploadPlaylistCover = async (id: string, file: File): Promise<ApiResponse<{ coverUrl: string }>> => {
    const { upload } = useApi()
    return await upload(`/playlists/${id}/cover`, file)
  }

  // 获取歌单统计
  const getPlaylistStats = async (id: string): Promise<ApiResponse<{
    playCount: number
    likeCount: number
    shareCount: number
    songCount: number
    totalDuration: number
    dailyPlays: number[]
  }>> => {
    return await get(`/playlists/${id}/stats`)
  }

  return {
    getPlaylists,
    getPlaylistById,
    createPlaylist,
    updatePlaylist,
    deletePlaylist,
    getPlaylistSongs,
    addSongToPlaylist,
    removeSongFromPlaylist,
    addSongsToPlaylist,
    removeSongsFromPlaylist,
    reorderPlaylistSongs,
    likePlaylist,
    unlikePlaylist,
    sharePlaylist,
    copyPlaylist,
    getUserPlaylists,
    getUserLikedPlaylists,
    searchPlaylists,
    getRecommendedPlaylists,
    getTrendingPlaylists,
    uploadPlaylistCover,
    getPlaylistStats
  }
}
