interface NotificationItem {
  id: string
  type: 'success' | 'error' | 'warning' | 'info'
  title: string
  message?: string
  duration?: number
  persistent?: boolean
  actions?: Array<{
    label: string
    action: () => void
    style?: 'primary' | 'secondary'
  }>
}

// 全局通知状态
const globalNotifications = ref<NotificationItem[]>([])

export const useNotification = () => {
  const notifications = globalNotifications

  // 生成唯一ID
  const generateId = () => {
    return Math.random().toString(36).substr(2, 9)
  }

  // 添加通知
  const addNotification = (notification: Omit<NotificationItem, 'id'>) => {
    const id = generateId()
    const newNotification: NotificationItem = {
      id,
      duration: 5000, // 默认5秒
      ...notification
    }

    notifications.value.push(newNotification)

    // 自动移除通知（除非是持久化通知）
    if (!newNotification.persistent && newNotification.duration) {
      setTimeout(() => {
        removeNotification(id)
      }, newNotification.duration)
    }

    return id
  }

  // 移除通知
  const removeNotification = (id: string) => {
    const index = notifications.value.findIndex(n => n.id === id)
    if (index > -1) {
      notifications.value.splice(index, 1)
    }
  }

  // 清除所有通知
  const clearAll = () => {
    notifications.value = []
  }

  // 成功通知
  const success = (title: string, message?: string, options?: Partial<NotificationItem>) => {
    return addNotification({
      type: 'success',
      title,
      message,
      ...options
    })
  }

  // 错误通知
  const error = (title: string, message?: string, options?: Partial<NotificationItem>) => {
    return addNotification({
      type: 'error',
      title,
      message,
      duration: 8000, // 错误通知显示更长时间
      ...options
    })
  }

  // 警告通知
  const warning = (title: string, message?: string, options?: Partial<NotificationItem>) => {
    return addNotification({
      type: 'warning',
      title,
      message,
      duration: 6000,
      ...options
    })
  }

  // 信息通知
  const info = (title: string, message?: string, options?: Partial<NotificationItem>) => {
    return addNotification({
      type: 'info',
      title,
      message,
      ...options
    })
  }

  // 确认对话框
  const confirm = (
    title: string,
    message?: string,
    onConfirm?: () => void,
    onCancel?: () => void
  ) => {
    return addNotification({
      type: 'warning',
      title,
      message,
      persistent: true,
      actions: [
        {
          label: '确认',
          style: 'primary',
          action: () => {
            onConfirm?.()
          }
        },
        {
          label: '取消',
          style: 'secondary',
          action: () => {
            onCancel?.()
          }
        }
      ]
    })
  }

  // 加载通知
  const loading = (title: string, message?: string) => {
    return addNotification({
      type: 'info',
      title,
      message,
      persistent: true
    })
  }

  // 更新通知
  const updateNotification = (id: string, updates: Partial<NotificationItem>) => {
    const notification = notifications.value.find(n => n.id === id)
    if (notification) {
      Object.assign(notification, updates)
    }
  }

  // 获取通知数量
  const count = computed(() => notifications.value.length)

  // 获取未读通知数量（这里简化为所有通知）
  const unreadCount = computed(() => notifications.value.length)

  // 检查是否有特定类型的通知
  const hasType = (type: NotificationItem['type']) => {
    return notifications.value.some(n => n.type === type)
  }

  // 获取特定类型的通知
  const getByType = (type: NotificationItem['type']) => {
    return notifications.value.filter(n => n.type === type)
  }

  return {
    notifications: readonly(notifications),
    count,
    unreadCount,
    addNotification,
    removeNotification,
    clearAll,
    success,
    error,
    warning,
    info,
    confirm,
    loading,
    updateNotification,
    hasType,
    getByType
  }
}
