<template>
  <div class="share-button">
    <!-- 分享按钮 -->
    <Button
      :variant="variant"
      :size="size"
      @click="showShareModal = true"
      :disabled="disabled"
    >
      <template #icon>
        <Icon name="share" :size="size === 'sm' ? 'sm' : 'md'" />
      </template>

      <span v-if="showText">
        {{ text || '分享' }}
      </span>
    </Button>

    <!-- 分享模态框 -->
    <UiModal
      v-model="showShareModal"
      title="分享"
      size="sm"
    >
      <div class="space-y-4">
        <!-- 分享预览 -->
        <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
          <div class="flex items-center space-x-3">
            <!-- 目标图片 -->
            <div class="w-12 h-12 rounded-lg bg-gradient-to-br from-primary-400 to-primary-600 flex items-center justify-center overflow-hidden">
              <img 
                v-if="targetData?.coverUrl" 
                :src="targetData.coverUrl" 
                :alt="targetData.title || targetData.name"
                class="w-full h-full object-cover"
              />
              <UiIcon 
                v-else 
                :name="targetType === 'music' ? 'musical-note' : 'queue-list'" 
                size="lg" 
                class="text-white" 
              />
            </div>
            
            <!-- 目标信息 -->
            <div class="flex-1 min-w-0">
              <h4 class="font-medium text-gray-900 dark:text-gray-100 truncate">
                {{ targetData?.title || targetData?.name }}
              </h4>
              <p class="text-sm text-gray-600 dark:text-gray-400 truncate">
                {{ targetData?.artist || targetData?.description }}
              </p>
            </div>
          </div>
        </div>

        <!-- 分享链接 -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            分享链接
          </label>
          <div class="flex items-center space-x-2">
            <input
              ref="shareUrlInput"
              :value="shareUrl"
              readonly
              class="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-gray-100 text-sm"
            />
            <UiButton
              variant="outline"
              size="sm"
              @click="copyShareUrl"
              :loading="copying"
            >
              <UiIcon name="clipboard" size="sm" />
              复制
            </UiButton>
          </div>
        </div>

        <!-- 社交媒体分享 -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
            分享到社交媒体
          </label>
          <div class="grid grid-cols-2 gap-3">
            <button
              v-for="platform in socialPlatforms"
              :key="platform.name"
              @click="shareToSocial(platform.name)"
              class="flex items-center justify-center space-x-2 p-3 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
              :class="platform.color"
            >
              <UiIcon :name="platform.icon" size="sm" />
              <span class="text-sm font-medium">{{ platform.label }}</span>
            </button>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700">
          <UiButton
            variant="ghost"
            @click="showShareModal = false"
          >
            取消
          </UiButton>
          <UiButton
            variant="primary"
            @click="handleShare"
            :loading="sharing"
          >
            确认分享
          </UiButton>
        </div>
      </div>
    </UiModal>
  </div>
</template>

<script setup lang="ts">
interface Props {
  targetType: 'music' | 'playlist'
  targetId: string
  targetData?: any
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost'
  size?: 'sm' | 'md' | 'lg'
  showText?: boolean
  text?: string
  disabled?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'outline',
  size: 'md',
  showText: true,
  disabled: false
})

const emit = defineEmits<{
  shared: [platform: string]
  error: [error: any]
}>()

const { shareTarget, getShareUrl } = useSocialApi()
const { showNotification } = useNotification()

const showShareModal = ref(false)
const shareUrl = ref('')
const copying = ref(false)
const sharing = ref(false)
const shareUrlInput = ref<HTMLInputElement>()

// 社交媒体平台配置
const socialPlatforms = [
  {
    name: 'wechat',
    label: '微信',
    icon: 'chat-bubble-left-right',
    color: 'text-green-600 hover:text-green-700'
  },
  {
    name: 'weibo',
    label: '微博',
    icon: 'at-symbol',
    color: 'text-red-600 hover:text-red-700'
  },
  {
    name: 'qq',
    label: 'QQ',
    icon: 'chat-bubble-oval-left',
    color: 'text-blue-600 hover:text-blue-700'
  },
  {
    name: 'twitter',
    label: 'Twitter',
    icon: 'hashtag',
    color: 'text-sky-600 hover:text-sky-700'
  }
]

// 生成分享链接
const generateShareUrl = async () => {
  try {
    const response = await getShareUrl(props.targetType, props.targetId)
    if (response.success) {
      shareUrl.value = response.data.shareUrl
    } else {
      // 生成默认分享链接
      shareUrl.value = `${window.location.origin}/${props.targetType}/${props.targetId}`
    }
  } catch (error) {
    console.error('Generate share URL error:', error)
    // 生成默认分享链接
    shareUrl.value = `${window.location.origin}/${props.targetType}/${props.targetId}`
  }
}

// 复制分享链接
const copyShareUrl = async () => {
  if (!shareUrl.value) return
  
  try {
    copying.value = true
    await navigator.clipboard.writeText(shareUrl.value)
    showNotification('链接已复制到剪贴板', 'success')
    
    // 选中输入框文本
    if (shareUrlInput.value) {
      shareUrlInput.value.select()
    }
  } catch (error) {
    console.error('Copy share URL error:', error)
    showNotification('复制失败', 'error')
  } finally {
    copying.value = false
  }
}

// 分享到社交媒体
const shareToSocial = async (platform: string) => {
  if (!shareUrl.value) return
  
  try {
    const title = props.targetData?.title || props.targetData?.name || '分享音乐'
    const description = props.targetData?.artist || props.targetData?.description || '来自 MusicDou'
    
    let shareUrlWithParams = ''
    
    switch (platform) {
      case 'wechat':
        // 微信分享需要特殊处理，这里只是示例
        showNotification('请复制链接手动分享到微信', 'info')
        await copyShareUrl()
        return
        
      case 'weibo':
        shareUrlWithParams = `https://service.weibo.com/share/share.php?url=${encodeURIComponent(shareUrl.value)}&title=${encodeURIComponent(`${title} - ${description}`)}`
        break
        
      case 'qq':
        shareUrlWithParams = `https://connect.qq.com/widget/shareqq/index.html?url=${encodeURIComponent(shareUrl.value)}&title=${encodeURIComponent(title)}&summary=${encodeURIComponent(description)}`
        break
        
      case 'twitter':
        shareUrlWithParams = `https://twitter.com/intent/tweet?url=${encodeURIComponent(shareUrl.value)}&text=${encodeURIComponent(`${title} - ${description}`)}`
        break
        
      default:
        showNotification('暂不支持该平台', 'warning')
        return
    }
    
    // 打开新窗口分享
    window.open(shareUrlWithParams, '_blank', 'width=600,height=400')
    
    // 记录分享行为
    await handleShare(platform)
    
    emit('shared', platform)
  } catch (error) {
    console.error('Share to social error:', error)
    showNotification('分享失败', 'error')
    emit('error', error)
  }
}

// 处理分享
const handleShare = async (platform?: string) => {
  try {
    sharing.value = true
    
    const response = await shareTarget(props.targetType, props.targetId, platform)
    
    if (response.success) {
      showNotification('分享成功', 'success')
      if (!platform) {
        showShareModal.value = false
      }
    }
  } catch (error) {
    console.error('Handle share error:', error)
    showNotification('分享失败', 'error')
    emit('error', error)
  } finally {
    sharing.value = false
  }
}

// 监听模态框显示状态
watch(showShareModal, (show) => {
  if (show && !shareUrl.value) {
    generateShareUrl()
  }
})
</script>

<style scoped>
.share-button {
  @apply inline-block;
}
</style>
