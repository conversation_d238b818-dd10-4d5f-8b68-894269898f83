<template>
  <div 
    v-if="playerStore.hasCurrentTrack" 
    class="fixed bottom-0 left-0 right-0 bg-white dark:bg-slate-800 border-t border-gray-200 dark:border-slate-700 shadow-lg z-50"
  >
    <div class="max-w-screen-xl mx-auto px-4 py-3">
      <div class="flex items-center justify-between">
        <!-- 歌曲信息 -->
        <div class="flex items-center space-x-3 flex-1 min-w-0">
          <div 
            class="w-12 h-12 bg-gray-200 dark:bg-slate-700 rounded-lg flex-shrink-0 overflow-hidden"
          >
            <img 
              v-if="currentTrack?.coverUrl" 
              :src="currentTrack.coverUrl" 
              :alt="currentTrack.title"
              class="w-full h-full object-cover"
            >
            <div 
              v-else 
              class="w-full h-full flex items-center justify-center"
            >
              <Icon name="musical-note" class="w-6 h-6 text-gray-400" />
            </div>
          </div>
          
          <div class="min-w-0 flex-1">
            <h4 class="text-sm font-medium text-gray-900 dark:text-white truncate">
              {{ currentTrack?.title || '未知歌曲' }}
            </h4>
            <p class="text-xs text-gray-500 dark:text-gray-400 truncate">
              {{ currentTrack?.artist || '未知艺术家' }}
            </p>
          </div>
        </div>

        <!-- 播放控制 -->
        <div class="flex items-center space-x-2 mx-6">
          <!-- 上一首 -->
          <Button
            variant="ghost"
            size="sm"
            :disabled="!playerStore.canPlayPrevious"
            @click="audioPlayer.playPrevious()"
            class="p-2"
          >
            <Icon name="backward" class="w-4 h-4" />
          </Button>

          <!-- 播放/暂停 -->
          <Button
            variant="primary"
            size="sm"
            @click="audioPlayer.togglePlay()"
            class="p-2"
          >
            <Icon
              :name="playerStore.state.isPlaying ? 'pause' : 'play'"
              class="w-4 h-4"
            />
          </Button>

          <!-- 下一首 -->
          <Button
            variant="ghost"
            size="sm"
            :disabled="!playerStore.canPlayNext"
            @click="audioPlayer.playNext()"
            class="p-2"
          >
            <Icon name="forward" class="w-4 h-4" />
          </Button>
        </div>

        <!-- 进度和音量控制 -->
        <div class="flex items-center space-x-4 flex-1 min-w-0">
          <!-- 进度条 -->
          <div class="flex items-center space-x-2 flex-1">
            <span class="text-xs text-gray-500 dark:text-gray-400 w-10 text-right">
              {{ formatTime(playerStore.state.currentTime) }}
            </span>
            
            <div class="flex-1 relative">
              <input
                type="range"
                :value="playerStore.state.currentTime"
                :max="playerStore.state.duration || 0"
                step="1"
                @input="onProgressChange"
                class="w-full h-1 bg-gray-200 dark:bg-slate-600 rounded-lg appearance-none cursor-pointer slider"
              >
            </div>
            
            <span class="text-xs text-gray-500 dark:text-gray-400 w-10">
              {{ formatTime(playerStore.state.duration) }}
            </span>
          </div>

          <!-- 播放模式控制 -->
          <div class="flex items-center space-x-1">
            <!-- 随机播放 -->
            <Button
              variant="ghost"
              size="sm"
              :class="{ 'text-primary-500': playerStore.state.shuffle }"
              @click="playerStore.toggleShuffle()"
              class="p-1"
            >
              <Icon name="arrows-right-left" class="w-4 h-4" />
            </Button>

            <!-- 循环模式 -->
            <Button
              variant="ghost"
              size="sm"
              :class="{ 'text-primary-500': playerStore.state.repeat !== 'none' }"
              @click="toggleRepeat"
              class="p-1"
            >
              <Icon 
                :name="repeatIcon" 
                class="w-4 h-4" 
              />
            </Button>
          </div>

          <!-- 音量控制 -->
          <div class="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              @click="toggleMute"
              class="p-1"
            >
              <Icon :name="volumeIcon" class="w-4 h-4" />
            </Button>
            
            <div class="w-20">
              <input
                type="range"
                :value="playerStore.state.volume * 100"
                min="0"
                max="100"
                step="1"
                @input="onVolumeChange"
                class="w-full h-1 bg-gray-200 dark:bg-slate-600 rounded-lg appearance-none cursor-pointer slider"
              >
            </div>
          </div>

          <!-- 播放队列 -->
          <Button
            variant="ghost"
            size="sm"
            @click="showQueue = !showQueue"
            class="p-1"
          >
            <Icon name="queue-list" class="w-4 h-4" />
          </Button>
        </div>
      </div>
    </div>

    <!-- 播放队列弹出层 -->
    <div 
      v-if="showQueue"
      class="absolute bottom-full right-0 w-80 max-h-96 bg-white dark:bg-slate-800 border border-gray-200 dark:border-slate-700 rounded-t-lg shadow-lg overflow-hidden"
    >
      <div class="p-3 border-b border-gray-200 dark:border-slate-700">
        <div class="flex items-center justify-between">
          <h3 class="text-sm font-medium text-gray-900 dark:text-white">
            播放队列 ({{ playerStore.state.queue.length }})
          </h3>
          <Button
            variant="ghost"
            size="sm"
            @click="showQueue = false"
            class="p-1"
          >
            <Icon name="x-mark" class="w-4 h-4" />
          </Button>
        </div>
      </div>
      
      <div class="overflow-y-auto max-h-80">
        <div
          v-for="(track, index) in playerStore.state.queue"
          :key="track.id"
          :class="[
            'flex items-center space-x-3 p-3 hover:bg-gray-50 dark:hover:bg-slate-700 cursor-pointer',
            { 'bg-primary-50 dark:bg-primary-900/20': index === playerStore.state.currentIndex }
          ]"
          @click="playQueueTrack(index)"
        >
          <div class="w-8 h-8 bg-gray-200 dark:bg-slate-700 rounded overflow-hidden flex-shrink-0">
            <img 
              v-if="track.coverUrl" 
              :src="track.coverUrl" 
              :alt="track.title"
              class="w-full h-full object-cover"
            >
            <div 
              v-else 
              class="w-full h-full flex items-center justify-center"
            >
              <Icon name="musical-note" class="w-4 h-4 text-gray-400" />
            </div>
          </div>
          
          <div class="flex-1 min-w-0">
            <p class="text-sm font-medium text-gray-900 dark:text-white truncate">
              {{ track.title }}
            </p>
            <p class="text-xs text-gray-500 dark:text-gray-400 truncate">
              {{ track.artist }}
            </p>
          </div>
          
          <div class="flex items-center space-x-1">
            <span class="text-xs text-gray-500 dark:text-gray-400">
              {{ formatTime(track.duration) }}
            </span>
            
            <Button
              variant="ghost"
              size="sm"
              @click.stop="removeFromQueue(index)"
              class="p-1 opacity-0 group-hover:opacity-100"
            >
              <Icon name="x-mark" class="w-3 h-3" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const playerStore = usePlayerStore()
const audioPlayer = useAudioPlayer()

// 响应式数据
const showQueue = ref(false)
const previousVolume = ref(0.8)

// 计算属性
const currentTrack = computed(() => playerStore.state.currentTrack)

const volumeIcon = computed(() => {
  const volume = playerStore.state.volume
  if (volume === 0) return 'speaker-x-mark'
  if (volume < 0.5) return 'speaker-wave'
  return 'speaker-wave'
})

const repeatIcon = computed(() => {
  switch (playerStore.state.repeat) {
    case 'one':
      return 'arrow-path'
    case 'all':
      return 'arrow-path'
    default:
      return 'arrow-path'
  }
})

// 方法
const formatTime = (seconds: number): string => {
  if (!seconds || isNaN(seconds)) return '0:00'
  
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = Math.floor(seconds % 60)
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
}

const onProgressChange = (event: Event) => {
  const target = event.target as HTMLInputElement
  const time = parseFloat(target.value)
  audioPlayer.seekTo(time)
}

const onVolumeChange = (event: Event) => {
  const target = event.target as HTMLInputElement
  const volume = parseFloat(target.value) / 100
  audioPlayer.setVolume(volume)
}

const toggleMute = () => {
  if (playerStore.state.volume > 0) {
    previousVolume.value = playerStore.state.volume
    audioPlayer.setVolume(0)
  } else {
    audioPlayer.setVolume(previousVolume.value)
  }
}

const toggleRepeat = () => {
  const modes: Array<'none' | 'one' | 'all'> = ['none', 'one', 'all']
  const currentIndex = modes.indexOf(playerStore.state.repeat)
  const nextIndex = (currentIndex + 1) % modes.length
  playerStore.setRepeat(modes[nextIndex])
}

const playQueueTrack = (index: number) => {
  playerStore.setCurrentIndex(index)
  showQueue.value = false
}

const removeFromQueue = (index: number) => {
  playerStore.removeFromQueue(index)
}

// 点击外部关闭队列
// onClickOutside(showQueue, () => {
//   showQueue.value = false
// })
</script>

<style scoped>
/* 自定义滑块样式 */
.slider::-webkit-slider-thumb {
  appearance: none;
  height: 12px;
  width: 12px;
  border-radius: 50%;
  background: #3b82f6;
  cursor: pointer;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.slider::-moz-range-thumb {
  height: 12px;
  width: 12px;
  border-radius: 50%;
  background: #3b82f6;
  cursor: pointer;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.slider::-webkit-slider-track {
  height: 4px;
  border-radius: 2px;
  background: #e5e7eb;
}

.slider::-moz-range-track {
  height: 4px;
  border-radius: 2px;
  background: #e5e7eb;
}

.dark .slider::-webkit-slider-track {
  background: #475569;
}

.dark .slider::-moz-range-track {
  background: #475569;
}
</style>
