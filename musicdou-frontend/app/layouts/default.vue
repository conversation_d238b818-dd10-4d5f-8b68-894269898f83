<template>
  <div :class="['h-screen', 'bg-gray-50', 'dark:bg-gray-900', 'flex', 'flex-col', 'overflow-hidden']">
    <!-- 顶部导航栏 - 固定位置 -->
    <header class="h-16 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between px-4 lg:px-6 flex-shrink-0 z-50">
      <!-- 左侧：Logo -->
      <div class="flex items-center gap-4">
        <NuxtLink to="/" class="flex items-center gap-2">
          <Icon name="musical-note" class="w-8 h-8 text-blue-500" />
          <span class="text-xl font-bold text-gray-900 dark:text-white">
            MusicDou
          </span>
        </NuxtLink>
      </div>

      <!-- 中间：搜索框 -->
      <div class="flex-1 max-w-md mx-4 hidden md:block">
        <div class="relative">
          <Icon
            name="magnifying-glass"
            class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400"
          />
          <input
            type="text"
            placeholder="搜索音乐、歌单、用户..."
            class="w-full pl-10 pr-4 py-2 bg-gray-100 dark:bg-gray-700 border border-transparent rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
          />
        </div>
      </div>

      <!-- 右侧：用户区域 -->
      <div class="flex items-center gap-3">
        <!-- 主题切换按钮 -->
        <ClientOnly>
          <button
            @click="toggleTheme"
            class="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
            :title="isDark ? '切换到浅色模式' : '切换到深色模式'"
          >
            <!-- 太阳图标 (深色模式时显示) -->
            <svg v-if="isDark" class="w-5 h-5 text-gray-600 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
            </svg>
            <!-- 月亮图标 (浅色模式时显示) -->
            <svg v-else class="w-5 h-5 text-gray-600 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
            </svg>
          </button>
          <template #fallback>
            <!-- 服务器端渲染时的占位符 -->
            <button class="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
              <svg class="w-5 h-5 text-gray-600 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
              </svg>
            </button>
          </template>
        </ClientOnly>

        <div class="relative" ref="userMenuRef">
          <!-- 用户头像 -->
          <button
            @click="toggleUserMenu"
            class="flex items-center gap-2 p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
          >
            <div class="w-8 h-8 rounded-full overflow-hidden bg-gradient-to-br from-blue-400 to-blue-600 flex items-center justify-center text-white text-sm font-medium">
              <img
                v-if="authStore.user?.avatar"
                :src="authStore.user.avatar"
                :alt="authStore.user.username"
                class="w-full h-full object-cover"
              />
              <span v-else>
                {{ authStore.user?.username?.charAt(0).toUpperCase() || 'U' }}
              </span>
            </div>
            <span v-if="authStore.user" class="text-sm font-medium text-gray-700 dark:text-gray-300 hidden lg:block">
              {{ authStore.user.username }}
            </span>
            <!-- 下拉箭头 -->
            <svg
              :class="[
                'w-4 h-4 text-gray-400 transition-transform duration-200 hidden lg:block',
                showUserMenu ? 'rotate-180' : ''
              ]"
              fill="currentColor"
              viewBox="0 0 24 24"
            >
              <path d="M7 10l5 5 5-5z"/>
            </svg>
          </button>

          <!-- 下拉菜单 -->
          <div
            v-show="showUserMenu"
            class="absolute right-0 top-full mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 transition-all duration-200 z-50"
          >
            <div class="py-2">
              <NuxtLink to="/profile" class="flex items-center gap-3 px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                </svg>
                个人信息
              </NuxtLink>
              <NuxtLink to="/settings" class="flex items-center gap-3 px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.82,11.69,4.82,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z"/>
                </svg>
                设置
              </NuxtLink>
              <div class="border-t border-gray-200 dark:border-gray-700 my-1"></div>
              <button @click="handleLogout" class="w-full flex items-center gap-3 px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-left">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"/>
                </svg>
                退出登录
              </button>
            </div>
          </div>
        </div>
      </div>
    </header>

    <!-- 主要内容区域 - 可滚动 -->
    <div class="flex flex-1 min-h-0 overflow-hidden">
      <!-- 左侧导航栏 (仅在主页面显示) - 固定宽度，内容可滚动 -->
      <aside class="w-64 h-full bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 hidden md:flex flex-shrink-0 flex-col">
        <!-- 可滚动的导航内容 -->
        <div class="flex-1 overflow-y-auto p-4">
          <!-- 推荐 -->
          <div class="mb-6">
            <h3 class="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-2">
              推荐
            </h3>
            <nav class="space-y-1">
              <NuxtLink to="/" class="flex items-center gap-3 px-3 py-2 text-sm text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700">
                <!-- 发现音乐 - 音乐播放图标 -->
                <svg class="w-4 h-4 text-blue-500" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z"/>
                </svg>
                发现音乐
              </NuxtLink>
              <NuxtLink to="/daily-recommend" class="flex items-center gap-3 px-3 py-2 text-sm text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700">
                <!-- 每日推荐 - 日历心形图标 -->
                <svg class="w-4 h-4 text-red-500" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 6l-2 2-2-2 2-2 2 2zm0 4l-2 2-2-2 2-2 2 2z"/>
                  <path d="M12 15.5c-1.25 0-2.27.86-2.27 1.93 0 .43.16.84.44 1.16.28.31.65.48 1.05.48h1.56c.4 0 .77-.17 1.05-.48.28-.32.44-.73.44-1.16 0-1.07-1.02-1.93-2.27-1.93z"/>
                </svg>
                每日推荐
              </NuxtLink>
              <NuxtLink to="/charts" class="flex items-center gap-3 px-3 py-2 text-sm text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700">
                <!-- 排行榜 - 奖杯排行图标 -->
                <svg class="w-4 h-4 text-yellow-500" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M7 4V2C7 1.45 7.45 1 8 1H16C16.55 1 17 1.45 17 2V4H20C20.55 4 21 4.45 21 5S20.55 6 20 6H19V7C19 10.31 16.31 13 13 13H11C7.69 13 5 10.31 5 7V6H4C3.45 6 3 5.55 3 5S3.45 4 4 4H7ZM9 3V4H15V3H9ZM7 6V7C7 9.21 8.79 11 11 11H13C15.21 11 17 9.21 17 7V6H7Z"/>
                  <path d="M12 15C10.9 15 10 15.9 10 17V21C10 22.1 10.9 23 12 23S14 22.1 14 21V17C14 15.9 13.1 15 12 15Z"/>
                  <path d="M8 17H6C5.45 17 5 17.45 5 18S5.45 19 6 19H8V17Z"/>
                  <path d="M18 17H16V19H18C18.55 19 19 18.55 19 18S18.55 17 18 17Z"/>
                </svg>
                排行榜
              </NuxtLink>
              <NuxtLink to="/latest-uploads" class="flex items-center gap-3 px-3 py-2 text-sm text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700">
                <!-- 最新上传 - 蓝色上传图标 -->
                <svg class="w-4 h-4 text-blue-500" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                  <path d="M12 11L12 16L14 14L12 11M10 14L12 11L10 14"/>
                </svg>
                最新上传
              </NuxtLink>
            </nav>
          </div>

          <!-- 社交 -->
          <div class="mb-6">
            <h3 class="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-2">
              社交
            </h3>
            <nav class="space-y-1">
              <NuxtLink to="/social/friends" class="flex items-center gap-3 px-3 py-2 text-sm text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700">
                <!-- 朋友 - 双人头像图标 -->
                <svg class="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M16 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zm4 18v-6h2.5l-2.54-7.63A1.5 1.5 0 0 0 18.54 7H17c-.8 0-1.54.37-2.01.99l-2.98 3.67a.5.5 0 0 0 .39.84h2.6v10h2z"/>
                  <path d="M12.5 11.5c.83 0 1.5-.67 1.5-1.5s-.67-1.5-1.5-1.5S11 9.17 11 10s.67 1.5 1.5 1.5z"/>
                  <path d="M5.5 6c1.11 0 2-.89 2-2s-.89-2-2-2-2 .89-2 2 .89 2 2 2zm1.5 16v-7H5.5l2.54-7.63A1.5 1.5 0 0 1 9.46 6H11c.8 0 1.54.37 2.01.99L15.99 10.66a.5.5 0 0 1-.39.84H13v11.5H7z"/>
                </svg>
                朋友
              </NuxtLink>
              <NuxtLink to="/social/feed" class="flex items-center gap-3 px-3 py-2 text-sm text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700">
                <!-- 动态 - 动态波纹图标 -->
                <svg class="w-4 h-4 text-purple-500" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.94-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z"/>
                </svg>
                动态
              </NuxtLink>
            </nav>
          </div>

          <!-- 歌单 -->
          <div>
            <div class="flex items-center justify-between mb-2">
              <h3 class="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                歌单
              </h3>
              <button
                @click="showCreatePlaylistModal = true"
                class="flex items-center gap-1 px-2 py-1 text-xs text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded transition-colors"
                title="新建歌单"
              >
                <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
                </svg>
                新建歌单
              </button>
            </div>
            <nav class="space-y-1">
              <NuxtLink to="/playlists/liked" class="flex items-center gap-3 px-3 py-2 text-sm text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700">
                <!-- 我喜欢的音乐 - 红色爱心图标 -->
                <svg class="w-4 h-4 text-red-500" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
                </svg>
                我喜欢的音乐
              </NuxtLink>

              <!-- 用户创建的歌单加载状态 -->
              <div v-if="userPlaylistsLoading" class="flex items-center gap-3 px-3 py-2 text-sm text-gray-500 dark:text-gray-400">
                <div class="w-4 h-4 border-2 border-gray-300 border-t-blue-500 rounded-full animate-spin"></div>
                加载中...
              </div>

              <!-- 用户创建的歌单列表 -->
              <NuxtLink
                v-for="playlist in userPlaylists"
                :key="playlist._id"
                :to="`/playlists/${playlist._id}`"
                class="flex items-center gap-3 px-3 py-2 text-sm text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
              >
                <!-- 歌单封面或默认图标 -->
                <div class="w-4 h-4 flex-shrink-0">
                  <img
                    v-if="playlist.coverImage"
                    :src="playlist.coverImage"
                    :alt="playlist.name"
                    class="w-full h-full object-cover rounded"
                  />
                  <svg v-else class="w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z"/>
                  </svg>
                </div>
                <span class="truncate">{{ playlist.name }}</span>
              </NuxtLink>
            </nav>
          </div>

          <!-- 收藏歌单 -->
          <div class="mt-6">
            <h3 class="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-2">
              收藏歌单
            </h3>
            <nav class="space-y-1">
              <!-- 加载状态 -->
              <div v-if="favoritePlaylistsLoading" class="flex items-center gap-3 px-3 py-2 text-sm text-gray-500 dark:text-gray-400">
                <div class="w-4 h-4 border-2 border-gray-300 border-t-blue-500 rounded-full animate-spin"></div>
                加载中...
              </div>

              <!-- 收藏的歌单列表 -->
              <NuxtLink
                v-for="playlist in favoritePlaylists"
                :key="playlist._id"
                :to="`/playlists/${playlist._id}`"
                class="flex items-center gap-3 px-3 py-2 text-sm text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
              >
                <!-- 歌单封面或默认图标 -->
                <div class="w-4 h-4 flex-shrink-0">
                  <img
                    v-if="playlist.coverImage"
                    :src="playlist.coverImage"
                    :alt="playlist.name"
                    class="w-full h-full object-cover rounded"
                  />
                  <svg v-else class="w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z"/>
                  </svg>
                </div>
                <span class="truncate">{{ playlist.name }}</span>
              </NuxtLink>

              <!-- 空状态 -->
              <div v-if="!favoritePlaylistsLoading && favoritePlaylists.length === 0" class="px-3 py-2 text-xs text-gray-500 dark:text-gray-400">
                暂无收藏的歌单
              </div>
            </nav>
          </div>
        </div>

        <!-- 底部固定区域：上传音乐 -->
        <div class="flex-shrink-0 p-4 border-t border-gray-200 dark:border-gray-700">
          <NuxtLink
            to="/upload"
            class="flex items-center gap-3 px-3 py-3 text-sm font-medium text-white bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg hover:from-blue-600 hover:to-purple-700 transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-105"
          >
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
              <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
              <path d="M12 11L12 16L14 14L12 11M10 14L12 11L10 14"/>
            </svg>
            上传音乐
          </NuxtLink>
        </div>
      </aside>

      <!-- 中间内容区域 - 主要滚动区域 -->
      <main class="flex-1 min-w-0 overflow-y-auto">
        <slot />
      </main>

      <!-- 右侧播放队列 (仅在主页面显示) - 固定宽度，内容可滚动 -->
      <aside class="w-80 bg-white dark:bg-gray-800 border-l border-gray-200 dark:border-gray-700 hidden lg:block flex-shrink-0">
        <div class="h-full overflow-y-auto p-4">
          <div class="flex items-center gap-2 mb-4">
            <Icon name="queue-list" class="w-5 h-5 text-gray-600 dark:text-gray-400" />
            <h2 class="text-lg font-semibold text-gray-900 dark:text-white">
              播放列表
            </h2>
            <span v-if="playerStore.hasQueue" class="text-xs text-gray-500 dark:text-gray-400">
              ({{ playerStore.state.queue.length }})
            </span>
          </div>

          <!-- 播放队列列表 -->
          <div v-if="playerStore.hasQueue" class="space-y-2">
            <div
              v-for="(track, index) in playerStore.state.queue"
              :key="track.id"
              :class="[
                'flex items-center gap-3 p-2 rounded-lg cursor-pointer transition-colors',
                index === playerStore.state.currentIndex
                  ? 'bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800'
                  : 'hover:bg-gray-50 dark:hover:bg-gray-700'
              ]"
              @click="playQueueTrack(index)"
            >
              <!-- 播放状态指示器 -->
              <div class="w-4 h-4 flex items-center justify-center flex-shrink-0">
                <Icon
                  v-if="index === playerStore.state.currentIndex && playerStore.state.isPlaying"
                  name="play"
                  class="w-3 h-3 text-blue-500"
                />
                <Icon
                  v-else-if="index === playerStore.state.currentIndex"
                  name="pause"
                  class="w-3 h-3 text-blue-500"
                />
                <span v-else class="text-xs text-gray-400">{{ index + 1 }}</span>
              </div>

              <!-- 歌曲信息 -->
              <div class="flex-1 min-w-0">
                <div class="text-sm font-medium text-gray-900 dark:text-white truncate">
                  {{ track.title }}
                </div>
                <div class="text-xs text-gray-500 dark:text-gray-400 truncate">
                  {{ track.artist }}
                </div>
              </div>

              <!-- 删除按钮 -->
              <button
                @click.stop="removeFromQueue(index)"
                class="w-6 h-6 flex items-center justify-center text-gray-400 hover:text-red-500 transition-colors"
                title="从队列中移除"
              >
                <Icon name="x-mark" class="w-4 h-4" />
              </button>
            </div>
          </div>

          <!-- 空状态 -->
          <div v-else class="text-center py-8">
            <Icon name="musical-note" class="w-16 h-16 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
            <p class="text-sm text-gray-500 dark:text-gray-400">
              暂无播放内容
            </p>
          </div>
        </div>
      </aside>
    </div>

    <!-- 底部播放器 - 固定位置 -->
    <footer class="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 flex-shrink-0 z-50">
      <!-- 进度条 -->
      <div
        class="relative w-full h-2 bg-gray-200 dark:bg-gray-600 rounded-full cursor-pointer group hover:h-3 transition-all duration-200"
        @mousedown="handleProgressMouseDown"
        @click="handleProgressClick"
        ref="progressContainer"
      >
        <div class="h-full bg-blue-500 rounded-full relative transition-all duration-100" :style="`width: ${playbackProgress}%`">
          <!-- 进度圆点 -->
          <div class="absolute right-0 top-1/2 transform -translate-y-1/2 w-3 h-3 bg-blue-500 rounded-full opacity-0 group-hover:opacity-100 group-hover:w-4 group-hover:h-4 transition-all duration-200 shadow-lg cursor-grab active:cursor-grabbing"></div>
        </div>
      </div>

      <!-- 播放器控制区域 -->
      <div class="h-20 flex items-center px-6 gap-4">
        <!-- 左侧：当前播放信息 -->
        <div class="flex items-center gap-4 flex-shrink-0">
          <div class="w-12 h-12 bg-gray-200 dark:bg-gray-600 rounded overflow-hidden">
            <img
              v-if="playerStore.state.currentTrack?.coverUrl"
              :src="playerStore.state.currentTrack.coverUrl"
              :alt="playerStore.state.currentTrack.title"
              class="w-full h-full object-cover"
            >
            <div
              v-else
              class="w-full h-full flex items-center justify-center"
            >
              <Icon name="musical-note" class="w-6 h-6 text-gray-400" />
            </div>
          </div>
          <div>
            <div class="text-sm font-medium text-gray-900 dark:text-white">
              {{ playerStore.state.currentTrack?.title || '暂无播放' }}
            </div>
            <div class="text-xs text-gray-500 dark:text-gray-400">
              {{ playerStore.state.currentTrack?.artist || '选择音乐开始播放' }}
            </div>
          </div>

          <!-- 时间显示 -->
          <div class="flex flex-col items-center justify-center text-xs text-gray-500 dark:text-gray-400 font-mono ml-4">
            <span>{{ currentTimeFormatted }}</span>
            <div class="w-8 border-t border-gray-300 dark:border-gray-600 my-1"></div>
            <span>{{ durationFormatted }}</span>
          </div>
        </div>

        <!-- 中间：歌词区域 -->
        <div class="flex-1 flex items-center justify-center px-4 min-w-0">
          <div class="text-center text-gray-600 dark:text-gray-300 italic truncate">
            <span class="text-xs sm:text-sm lg:text-base xl:text-lg">
              🎵 暂无歌词，享受纯音乐的美妙 🎵
            </span>
          </div>
        </div>

        <!-- 右侧：播放控制 + 音量控制 -->
        <div class="flex items-center gap-4 flex-shrink-0">
          <!-- 播放控制 -->
          <div class="flex items-center gap-3">
            <!-- 上一曲 -->
            <button
              @click="handlePrevious"
              :disabled="!playerStore.canPlayPrevious"
              :class="[
                'p-2 rounded-full border transition-colors',
                playerStore.canPlayPrevious
                  ? 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500 text-gray-600 dark:text-gray-400'
                  : 'border-gray-200 dark:border-gray-700 text-gray-300 dark:text-gray-600 cursor-not-allowed'
              ]"
            >
              <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                <path d="M6 6h2v12H6zm3.5 6l8.5 6V6z"/>
              </svg>
            </button>

            <!-- 播放/暂停 (最大的圆形按钮) -->
            <button
              @click="handleTogglePlay"
              :disabled="!playerStore.hasCurrentTrack"
              :class="[
                'p-4 rounded-full transition-colors shadow-lg',
                playerStore.hasCurrentTrack
                  ? 'bg-blue-500 hover:bg-blue-600 text-white'
                  : 'bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed'
              ]"
            >
              <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                <path v-if="playerStore.state.isPlaying" d="M6 4h4v16H6V4zm8 0h4v16h-4V4z"/>
                <path v-else d="M8 5v14l11-7z"/>
              </svg>
            </button>

            <!-- 下一曲 -->
            <button
              @click="handleNext"
              :disabled="!playerStore.canPlayNext"
              :class="[
                'p-2 rounded-full border transition-colors',
                playerStore.canPlayNext
                  ? 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500 text-gray-600 dark:text-gray-400'
                  : 'border-gray-200 dark:border-gray-700 text-gray-300 dark:text-gray-600 cursor-not-allowed'
              ]"
            >
              <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                <path d="M6 18l8.5-6L6 6v12zM16 6v12h2V6h-2z"/>
              </svg>
            </button>
          </div>

          <!-- 音量控制 -->
          <div class="flex items-center gap-3 ml-6">
            <!-- 喇叭图标 -->
            <svg class="w-5 h-5 text-gray-600 dark:text-gray-400" fill="currentColor" viewBox="0 0 24 24">
              <path d="M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z"/>
            </svg>

            <!-- 音量滑块 -->
            <div
              class="relative w-24 h-1 bg-gray-200 dark:bg-gray-600 rounded-full cursor-pointer group hover:h-2 transition-all duration-200"
              @mousedown="handleVolumeMouseDown"
              @click="handleVolumeClick"
              ref="volumeContainer"
            >
              <div class="h-full bg-blue-500 rounded-full relative transition-all duration-200" :style="`width: ${volumePercentage}%`">
                <!-- 音量滑块圆点 -->
                <div class="absolute right-0 top-1/2 transform -translate-y-1/2 w-3 h-3 bg-blue-500 rounded-full opacity-0 group-hover:opacity-100 group-hover:w-4 group-hover:h-4 transition-all duration-200 shadow-lg cursor-grab active:cursor-grabbing"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  </div>

  <!-- 新建歌单模态框 -->
  <div
    v-if="showCreatePlaylistModal"
    class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
    @click.self="showCreatePlaylistModal = false"
  >
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
        新建歌单
      </h3>

      <form @submit.prevent="handleCreatePlaylist">
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            歌单名称
          </label>
          <input
            v-model="newPlaylistName"
            type="text"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
            placeholder="请输入歌单名称"
            required
          />
        </div>

        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            歌单描述（可选）
          </label>
          <textarea
            v-model="newPlaylistDescription"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
            placeholder="请输入歌单描述"
            rows="3"
          ></textarea>
        </div>

        <div class="mb-6">
          <label class="flex items-center">
            <input
              v-model="newPlaylistIsPublic"
              type="checkbox"
              class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
            />
            <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">
              公开歌单
            </span>
          </label>
        </div>

        <div class="flex justify-end gap-3">
          <button
            type="button"
            @click="showCreatePlaylistModal = false"
            class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
          >
            取消
          </button>
          <button
            type="submit"
            :disabled="createPlaylistLoading"
            class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <span v-if="createPlaylistLoading" class="flex items-center gap-2">
              <div class="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              创建中...
            </span>
            <span v-else>创建歌单</span>
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
// 导入Vue响应式函数
import { ref, onMounted, onUnmounted, computed } from 'vue'

// 导入认证store
const authStore = useAuthStore()

// 导入播放器store
const playerStore = usePlayerStore()

// 获取音频播放器实例（在组件初始化时获取一次）
const audioPlayer = useAudioPlayer()

// 路由信息
const route = useRoute()

// 用户菜单状态
const showUserMenu = ref(false)
const userMenuRef = ref<HTMLElement>()

// 新建歌单模态框状态
const showCreatePlaylistModal = ref(false)
const newPlaylistName = ref('')
const newPlaylistDescription = ref('')
const newPlaylistIsPublic = ref(true)
const createPlaylistLoading = ref(false)

// 用户歌单相关状态
const userPlaylists = ref<any[]>([])
const userPlaylistsLoading = ref(false)

// 收藏歌单相关状态
const favoritePlaylists = ref<any[]>([])
const favoritePlaylistsLoading = ref(false)

// 主题切换功能 - 服务器端渲染时使用默认值避免hydration不匹配
const isDark = ref(false)

// 在服务器端渲染时确保使用默认主题状态
if (import.meta.server) {
  isDark.value = false
}

// 切换用户菜单
const toggleUserMenu = () => {
  showUserMenu.value = !showUserMenu.value
}

// 点击外部关闭用户菜单
const handleClickOutside = (event: MouseEvent) => {
  if (userMenuRef.value && !userMenuRef.value.contains(event.target as Node)) {
    showUserMenu.value = false
  }
}

// 添加和移除事件监听器
onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  try {
    document.removeEventListener('click', handleClickOutside)
  } catch (error) {
    console.warn('Error removing event listener:', error)
  }
})

// 音量控制功能
const volumeContainer = ref<HTMLElement>()
const isDragging = ref(false)

// 计算音量百分比
const volumePercentage = computed(() => Math.round(playerStore.state.volume * 100))

// 播放进度控制功能
const progressContainer = ref<HTMLElement>()
const isProgressDragging = ref(false)

// 计算播放进度百分比
const playbackProgress = computed(() => {
  const { currentTime, duration } = playerStore.state
  if (!duration || duration === 0) return 0

  // 如果正在拖拽，使用预览时间；否则使用实际播放时间
  const displayTime = isProgressDragging.value && playerStore.previewTime !== null
    ? playerStore.previewTime
    : currentTime

  return Math.min(100, (displayTime / duration) * 100)
})

// 格式化时间显示
const formatTime = (seconds: number): string => {
  if (!seconds || isNaN(seconds)) return '0:00'

  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = Math.floor(seconds % 60)
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
}

// 当前播放时间和总时长
const currentTimeFormatted = computed(() => {
  // 如果正在拖拽，显示预览时间；否则显示实际播放时间
  const displayTime = isProgressDragging.value && playerStore.previewTime !== null
    ? playerStore.previewTime
    : playerStore.state.currentTime
  return formatTime(displayTime)
})
const durationFormatted = computed(() => formatTime(playerStore.state.duration))

// Vue音量控制事件处理
const updateVolume = (clientX: number) => {
  if (!volumeContainer.value) return

  const rect = volumeContainer.value.getBoundingClientRect()
  const percentage = Math.max(0, Math.min(100, ((clientX - rect.left) / rect.width) * 100))
  const volume = percentage / 100

  // 使用已获取的音频播放器实例设置音量
  audioPlayer.setVolume(volume)
}

const handleVolumeClick = (e: MouseEvent) => {
  if (!isDragging.value) {
    updateVolume(e.clientX)
  }
}

const handleVolumeMouseDown = (e: MouseEvent) => {
  isDragging.value = true
  updateVolume(e.clientX)
  e.preventDefault()

  const handleMouseMove = (e: MouseEvent) => {
    if (isDragging.value) {
      updateVolume(e.clientX)
    }
  }

  const handleMouseUp = () => {
    isDragging.value = false
    document.removeEventListener('mousemove', handleMouseMove)
    document.removeEventListener('mouseup', handleMouseUp)
  }

  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
}

// 播放进度控制事件处理
const updateProgress = (clientX: number, shouldSeek: boolean = true) => {
  if (!progressContainer.value) return

  const rect = progressContainer.value.getBoundingClientRect()
  const percentage = Math.max(0, Math.min(100, ((clientX - rect.left) / rect.width) * 100))

  // 计算新的播放时间
  const newTime = (percentage / 100) * playerStore.state.duration

  if (shouldSeek) {
    // 真正跳转到指定时间
    audioPlayer.seekTo(newTime)
  } else {
    // 仅更新UI显示，不跳转音频
    playerStore.setPreviewTime(newTime)
  }
}

const handleProgressClick = (e: MouseEvent) => {
  if (!isProgressDragging.value && playerStore.state.duration > 0) {
    updateProgress(e.clientX, true) // 点击时立即跳转
  }
}

const handleProgressMouseDown = (e: MouseEvent) => {
  if (playerStore.state.duration <= 0) return

  isProgressDragging.value = true
  updateProgress(e.clientX, false) // 开始拖拽时不跳转，只更新预览
  e.preventDefault()

  const handleMouseMove = (e: MouseEvent) => {
    if (isProgressDragging.value) {
      updateProgress(e.clientX, false) // 拖拽过程中不跳转，只更新预览
    }
  }

  const handleMouseUp = (e: MouseEvent) => {
    if (isProgressDragging.value) {
      updateProgress(e.clientX, true) // 松开时才真正跳转
    }
    isProgressDragging.value = false
    playerStore.setPreviewTime(null) // 清除预览时间
    document.removeEventListener('mousemove', handleMouseMove)
    document.removeEventListener('mouseup', handleMouseUp)
  }

  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
}

// 主题切换函数
const toggleTheme = () => {
  isDark.value = !isDark.value

  if (isDark.value) {
    document.documentElement.classList.add('dark')
    localStorage.setItem('theme', 'dark')
  } else {
    document.documentElement.classList.remove('dark')
    localStorage.setItem('theme', 'light')
  }
}

// 退出登录函数
const handleLogout = async () => {
  try {
    // 关闭用户菜单
    showUserMenu.value = false
    // 使用认证store的logout方法
    const authStore = useAuthStore()
    await authStore.logout()
  } catch (error) {
    console.error('退出登录失败:', error)
    // 即使API调用失败，也要清除本地token
    const token = useCookie('auth-token')
    token.value = null
    await navigateTo('/login')
  }
}

// 获取收藏的歌单
const fetchFavoritePlaylists = async () => {
  try {
    favoritePlaylistsLoading.value = true
    const { getUserLikedPlaylists } = usePlaylistApi()

    const response = await getUserLikedPlaylists({
      page: 1,
      limit: 10 // 侧边栏只显示前10个收藏的歌单
    })

    if (response.success && response.data?.data) {
      favoritePlaylists.value = response.data.data
    }
  } catch (error) {
    console.error('获取收藏歌单失败:', error)
  } finally {
    favoritePlaylistsLoading.value = false
  }
}

// 获取用户创建的歌单
const fetchUserPlaylists = async () => {
  try {
    userPlaylistsLoading.value = true
    const { getUserPlaylists } = usePlaylistApi()

    // 获取当前用户ID
    const userId = authStore.user?.id
    if (!userId) return

    const response = await getUserPlaylists(userId, {
      page: 1,
      limit: 10, // 侧边栏只显示前10个歌单
      includePrivate: true // 包括私有歌单，因为这是用户自己的歌单
    })

    if (response.success && response.data?.data) {
      userPlaylists.value = response.data.data
    }
  } catch (error) {
    console.error('获取用户歌单失败:', error)
  } finally {
    userPlaylistsLoading.value = false
  }
}

// 创建歌单处理函数
const handleCreatePlaylist = async () => {
  try {
    createPlaylistLoading.value = true
    const { createPlaylist } = usePlaylistApi()

    const response = await createPlaylist({
      name: newPlaylistName.value,
      description: newPlaylistDescription.value,
      isPublic: newPlaylistIsPublic.value
    })

    if (response.success) {
      // 重置表单
      newPlaylistName.value = ''
      newPlaylistDescription.value = ''
      newPlaylistIsPublic.value = true
      showCreatePlaylistModal.value = false

      // 显示成功消息
      const notification = useNotification()
      notification.success('创建成功', '歌单已创建')

      // 刷新用户歌单列表
      await fetchUserPlaylists()

      // 可选：跳转到新创建的歌单页面
      if (response.data?.id) {
        await navigateTo(`/playlists/${response.data.id}`)
      }
    }
  } catch (error) {
    console.error('创建歌单失败:', error)
    const notification = useNotification()
    notification.error('创建失败', '请稍后重试')
  } finally {
    createPlaylistLoading.value = false
  }
}

// 播放队列相关方法
const playQueueTrack = async (index: number) => {
  try {
    const track = playerStore.state.queue[index]
    if (track) {
      // 直接使用音频播放器播放指定歌曲
      const audioPlayer = useAudioPlayer()
      await audioPlayer.playTrack(track as any)
      // 更新当前索引
      playerStore.setCurrentIndex(index)
    }
  } catch (error) {
    console.error('播放队列歌曲失败:', error)
  }
}

const removeFromQueue = (index: number) => {
  playerStore.removeFromQueue(index)
}

// 底部播放器控制方法
const handleTogglePlay = async () => {
  try {
    const audioPlayer = useAudioPlayer()
    audioPlayer.togglePlay()
  } catch (error) {
    console.error('切换播放状态失败:', error)
  }
}

const handlePrevious = async () => {
  try {
    const audioPlayer = useAudioPlayer()
    await audioPlayer.playPrevious()
  } catch (error) {
    console.error('播放上一首失败:', error)
  }
}

const handleNext = async () => {
  try {
    const audioPlayer = useAudioPlayer()
    await audioPlayer.playNext()
  } catch (error) {
    console.error('播放下一首失败:', error)
  }
}

// 初始化主题和用户认证状态
onMounted(async () => {
  console.log("default布局 onMounted 执行了！");
  console.log("当前时间:", new Date().toLocaleTimeString());

  if (import.meta.client) {
    // 初始化主题
    const savedTheme = localStorage.getItem('theme')
    const systemDark = window.matchMedia('(prefers-color-scheme: dark)').matches

    isDark.value = savedTheme === 'dark' || (!savedTheme && systemDark)

    if (isDark.value) {
      document.documentElement.classList.add('dark')
    } else {
      document.documentElement.classList.remove('dark')
    }

    // 初始化用户认证状态
    try {
      await authStore.initAuth()
      console.log('用户认证状态初始化完成:', authStore.user)

      // 如果用户已登录，获取收藏的歌单和用户歌单
      if (authStore.user) {
        await Promise.all([
          fetchFavoritePlaylists(),
          fetchUserPlaylists()
        ])
      }
    } catch (error) {
      console.warn('用户认证状态初始化失败:', error)
    }
  }
})
</script>
