<template>
  <div class="container mx-auto p-6">
    <h1 class="text-2xl font-bold mb-6">修复测试页面</h1>
    
    <!-- 测试组件是否正常工作 -->
    <div class="space-y-6">
      <div class="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
        <h2 class="text-lg font-semibold mb-4">组件测试</h2>
        
        <!-- 测试 Button -->
        <div class="space-x-2 mb-4">
          <Button variant="primary">主要按钮</Button>
          <Button variant="secondary">次要按钮</Button>
          <Button variant="outline">轮廓按钮</Button>
        </div>

        <!-- 测试 Icon -->
        <div class="flex space-x-2 mb-4">
          <Icon name="heart" class="w-6 h-6 text-red-500" />
          <Icon name="play" class="w-6 h-6 text-blue-500" />
          <Icon name="pause" class="w-6 h-6 text-gray-500" />
        </div>

        <!-- 测试 SocialShareButton (如果存在) -->
        <div class="text-sm text-gray-600">
          SocialShareButton 组件测试 (可能需要单独实现)
        </div>
      </div>
      
      <!-- 测试API调用 -->
      <div class="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
        <h2 class="text-lg font-semibold mb-4">API测试</h2>
        
        <div class="space-y-2">
          <button 
            @click="testRecentTracks"
            class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
          >
            测试最近播放API
          </button>
          
          <button 
            @click="testRecommendedPlaylists"
            class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600"
          >
            测试推荐歌单API
          </button>
        </div>
        
        <div v-if="apiResults" class="mt-4 p-3 bg-gray-100 dark:bg-gray-700 rounded">
          <pre>{{ JSON.stringify(apiResults, null, 2) }}</pre>
        </div>
      </div>
      
      <!-- 测试主题切换 -->
      <div class="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
        <h2 class="text-lg font-semibold mb-4">主题测试</h2>
        <p class="text-gray-600 dark:text-gray-300">
          这个文本应该在浅色模式下是深色，在深色模式下是浅色。
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const { getRecentTracks } = useMusicApi()
const { getRecommendedPlaylists } = usePlaylistApi()

const apiResults = ref(null)

const testRecentTracks = async () => {
  try {
    const response = await getRecentTracks({ limit: 6 })
    apiResults.value = { type: 'recentTracks', data: response }
  } catch (error) {
    apiResults.value = { type: 'recentTracks', error: error.message }
  }
}

const testRecommendedPlaylists = async () => {
  try {
    const response = await getRecommendedPlaylists({ limit: 8 })
    apiResults.value = { type: 'recommendedPlaylists', data: response }
  } catch (error) {
    apiResults.value = { type: 'recommendedPlaylists', error: error.message }
  }
}
</script>
