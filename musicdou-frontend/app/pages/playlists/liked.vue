<template>
  <div class="h-full bg-gray-50 dark:bg-gray-900 overflow-y-auto">
    <div class="max-w-6xl mx-auto px-6 py-6">
      <!-- 加载状态 -->
      <div v-if="loading" class="flex items-center justify-center py-16">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-red-600"></div>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="text-center py-16">
        <Icon name="exclamation-triangle" class="w-16 h-16 text-red-500 mx-auto mb-4" />
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
          加载失败
        </h3>
        <p class="text-gray-500 dark:text-gray-400 mb-6">
          {{ error }}
        </p>
        <Button @click="fetchLikedMusic(1)">
          <Icon name="arrow-path" class="w-4 h-4 mr-2" />
          重试
        </Button>
      </div>

      <!-- 内容区域 -->
      <div v-else>
        <!-- 歌单头部 -->
        <div class="flex items-start gap-6 mb-8">
          <div class="w-32 h-32 sm:w-40 sm:h-40 lg:w-48 lg:h-48 bg-gradient-to-br from-red-400 to-pink-500 rounded-lg flex items-center justify-center shadow-lg flex-shrink-0">
            <Icon name="heart" class="w-12 h-12 sm:w-16 sm:h-16 lg:w-24 lg:h-24 text-white" />
          </div>
          <div class="flex-1 min-w-0">
            <h1 class="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 dark:text-white mb-2">
              我喜欢的音乐
            </h1>
            <p class="text-gray-600 dark:text-gray-400 mb-4">
              收藏你最爱的歌曲
            </p>
            <div class="flex items-center gap-2 sm:gap-4 text-xs sm:text-sm text-gray-500 dark:text-gray-400 mb-4 sm:mb-6 flex-wrap">
              <span>{{ user?.username || '用户' }}</span>
              <span>•</span>
              <span>{{ likedMusic.length }} 首歌曲</span>
              <span>•</span>
              <span class="hidden sm:inline">最后更新：{{ formatLastUpdate() }}</span>
            </div>
            <div class="flex items-center gap-2 sm:gap-3 flex-wrap">
              <Button
                size="lg"
                icon="play"
                icon-position="left"
                class="px-3 sm:px-4 lg:px-6 text-xs sm:text-sm lg:text-base"
                :disabled="likedMusic.length === 0"
                @click="playAll"
              >
                <span class="hidden sm:inline">播放全部</span>
                <span class="sm:hidden">播放</span>
              </Button>
              <Button
                size="lg"
                variant="outline"
                icon="arrow-path"
                icon-position="left"
                class="px-3 sm:px-4 lg:px-6 text-xs sm:text-sm lg:text-base"
                :disabled="likedMusic.length === 0"
                @click="shufflePlay"
              >
                <span class="hidden sm:inline">随机播放</span>
                <span class="sm:hidden">随机</span>
              </Button>
              <Button
                size="lg"
                variant="ghost"
                icon="share"
                class="px-2 sm:px-3"
                @click="sharePlaylist"
              />
            </div>
          </div>
        </div>

        <!-- 歌曲列表 -->
        <Card>
          <div v-if="likedMusic.length > 0" class="space-y-2">
            <div
              v-for="(like, index) in likedMusic"
              :key="like.id"
              class="flex items-center gap-4 p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors cursor-pointer group"
              @click="playMusic(like.target, index)"
            >
              <div class="w-8 text-center">
                <span class="text-gray-500 dark:text-gray-400 group-hover:hidden">
                  {{ index + 1 }}
                </span>
                <Icon name="play" class="w-4 h-4 text-gray-600 dark:text-gray-400 hidden group-hover:block" />
              </div>
              <div class="w-12 h-12 rounded-lg overflow-hidden bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center">
                <img
                  v-if="like.target?.coverUrl"
                  :src="like.target.coverUrl"
                  :alt="like.target.title"
                  class="w-full h-full object-cover"
                />
                <Icon v-else name="musical-note" class="w-6 h-6 text-white" />
              </div>
              <div class="flex-1 min-w-0">
                <h3 class="font-medium text-gray-900 dark:text-white truncate">
                  {{ like.target?.title || '未知歌曲' }}
                </h3>
                <p class="text-sm text-gray-500 dark:text-gray-400 truncate">
                  {{ like.target?.artist || '未知艺术家' }}
                </p>
              </div>
              <div class="text-sm text-gray-500 dark:text-gray-400 hidden md:block">
                {{ like.target?.album || '未知专辑' }}
              </div>
              <div class="text-sm text-gray-500 dark:text-gray-400">
                {{ formatDuration(like.target?.duration) }}
              </div>
              <div class="flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
                <Button
                  size="sm"
                  variant="ghost"
                  @click.stop="unlikeMusic(like.target.id, index)"
                  :disabled="unlikingIds.includes(like.target.id)"
                >
                  <Icon
                    :name="unlikingIds.includes(like.target.id) ? 'arrow-path' : 'heart'"
                    :solid="!unlikingIds.includes(like.target.id)"
                    :class="[
                      'w-4 h-4',
                      unlikingIds.includes(like.target.id) ? 'animate-spin text-gray-400' : 'text-red-500'
                    ]"
                  />
                </Button>
                <Button size="sm" variant="ghost" @click.stop="showMusicMenu(like.target, $event)">
                  <Icon name="ellipsis-horizontal" class="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>

          <!-- 空状态 -->
          <div v-else class="text-center py-16">
            <Icon name="heart" class="w-16 h-16 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
              还没有喜欢的音乐
            </h3>
            <p class="text-gray-500 dark:text-gray-400 mb-6">
              点击歌曲旁的爱心按钮来收藏你喜欢的音乐
            </p>
            <Button
              icon="magnifying-glass"
              icon-position="left"
              @click="navigateTo('/')"
            >
              发现音乐
            </Button>
          </div>
        </Card>

        <!-- 分页 -->
        <div v-if="pagination && pagination.pages > 1" class="mt-6 flex justify-center">
          <div class="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              :disabled="pagination.page <= 1"
              @click="changePage(pagination.page - 1)"
            >
              <Icon name="chevron-left" class="w-4 h-4" />
            </Button>
            <span class="text-sm text-gray-600 dark:text-gray-400 px-4">
              第 {{ pagination.page }} 页，共 {{ pagination.pages }} 页
            </span>
            <Button
              variant="outline"
              size="sm"
              :disabled="pagination.page >= pagination.pages"
              @click="changePage(pagination.page + 1)"
            >
              <Icon name="chevron-right" class="w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Music, PaginatedResponse } from '~/types'

const authStore = useAuthStore()
const playerStore = usePlayerStore()
const { getUserLikes, unlikeTarget } = useSocialApi()
const { getMusicPlayUrl, recordPlay } = useMusicApi()
const { success: showSuccess, error: showError, info: showInfo } = useNotification()

const user = computed(() => authStore.user)

// 响应式数据
const loading = ref(true)
const error = ref<string | null>(null)
const likedMusic = ref<any[]>([])
const pagination = ref<any>(null)
const currentPage = ref(1)
const unlikingIds = ref<string[]>([])

// 获取用户喜欢的音乐
const fetchLikedMusic = async (page = 1) => {
  try {
    console.log('=== 开始获取喜欢的音乐 ===')
    loading.value = true
    error.value = null

    // 检查用户是否登录，支持 id 和 _id 两种格式
    const userId = user.value?.id || user.value?._id
    console.log('当前用户信息:', user.value)
    console.log('提取的用户ID:', userId)

    if (!userId) {
      console.error('用户ID不存在，用户对象:', user.value)
      throw new Error('用户未登录')
    }

    console.log('准备调用 getUserLikes API...')
    console.log('参数:', { userId, page, limit: 20, targetType: 'music' })

    const response = await getUserLikes(userId, {
      page,
      limit: 20,
      targetType: 'music'
    })

    console.log('API响应:', response)

    if (response.success) {
      likedMusic.value = response.data.likes || []
      pagination.value = response.data.pagination
      currentPage.value = page
    } else {
      throw new Error(response.message || '获取喜欢的音乐失败')
    }
  } catch (err: any) {
    error.value = err.message || '获取喜欢的音乐失败'
    console.error('获取喜欢的音乐失败:', err)
  } finally {
    loading.value = false
  }
}

// 取消点赞音乐
const unlikeMusic = async (musicId: string, index: number) => {
  try {
    unlikingIds.value.push(musicId)

    const response = await unlikeTarget('music', musicId)

    if (response.success) {
      // 从列表中移除
      likedMusic.value.splice(index, 1)
      showSuccess('已取消喜欢')

      // 更新分页信息
      if (pagination.value) {
        pagination.value.total -= 1
        // 如果当前页没有数据了，跳转到上一页
        if (likedMusic.value.length === 0 && currentPage.value > 1) {
          await fetchLikedMusic(currentPage.value - 1)
        }
      }
    } else {
      throw new Error(response.message || '取消喜欢失败')
    }
  } catch (err: any) {
    showError(err.message || '取消喜欢失败')
    console.error('取消喜欢失败:', err)
  } finally {
    unlikingIds.value = unlikingIds.value.filter(id => id !== musicId)
  }
}

// 播放音乐
const playMusic = async (music: Music, index: number) => {
  if (!music) return

  try {
    console.log('开始播放音乐:', music.title)

    // 获取音乐播放URL
    console.log('获取播放URL...')
    const { data: urlData } = await getMusicPlayUrl(music.id)
    console.log('播放URL获取成功:', urlData.playUrl)

    // 创建包含播放URL的音乐对象
    const musicWithUrl = { ...music, url: urlData.playUrl }

    // 记录播放行为
    await recordPlay(music.id)

    // 将喜欢的音乐列表转换为播放队列（也需要获取播放URL）
    const musicList = likedMusic.value
      .map(like => like.target)
      .filter(Boolean) as Music[]

    // 检查队列中是否已存在该歌曲
    const existingIndex = playerStore.state.queue.findIndex(t => t.id === music.id)

    if (existingIndex !== -1) {
      // 如果已存在，直接跳转到该歌曲
      playerStore.setCurrentIndex(existingIndex)
      showSuccess('已切换到该歌曲')
    } else {
      // 播放当前歌曲，并设置播放队列
      const audioPlayer = useAudioPlayer()
      await audioPlayer.playTrack(musicWithUrl, [musicWithUrl])
      showSuccess('开始播放')
    }
  } catch (error) {
    console.error('播放失败:', error)
    showError('播放失败，请重试')
  }
}

// 播放全部
const playAll = async () => {
  if (likedMusic.value.length === 0) return

  try {
    const musicList = likedMusic.value
      .map(like => like.target)
      .filter(Boolean) as Music[]

    if (musicList.length > 0) {
      // 播放第一首歌曲
      await playMusic(musicList[0], 0)
      showSuccess(`开始播放全部 ${musicList.length} 首歌曲`)
    }
  } catch (error) {
    console.error('播放全部失败:', error)
    showError('播放全部失败，请重试')
  }
}

// 随机播放
const shufflePlay = async () => {
  if (likedMusic.value.length === 0) return

  try {
    const musicList = likedMusic.value
      .map(like => like.target)
      .filter(Boolean) as Music[]

    if (musicList.length > 0) {
      // 随机选择一首歌开始播放
      const randomIndex = Math.floor(Math.random() * musicList.length)
      await playMusic(musicList[randomIndex], randomIndex)
      playerStore.toggleShuffle() // 开启随机播放模式
      showSuccess(`随机播放 ${musicList.length} 首歌曲`)
    }
  } catch (error) {
    console.error('随机播放失败:', error)
    showError('随机播放失败，请重试')
  }
}

// 分享歌单
const sharePlaylist = () => {
  // TODO: 实现分享功能
  showInfo('分享功能开发中')
}

// 显示音乐菜单
const showMusicMenu = (music: Music, event: Event) => {
  // TODO: 实现右键菜单
  console.log('显示音乐菜单:', music)
}

// 切换页面
const changePage = (page: number) => {
  if (page >= 1 && pagination.value && page <= pagination.value.pages) {
    fetchLikedMusic(page)
  }
}

// 格式化时长
const formatDuration = (seconds?: number): string => {
  if (!seconds) return '0:00.00'

  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes}:${remainingSeconds.toFixed(2).padStart(5, '0')}`
}

// 格式化最后更新时间
const formatLastUpdate = (): string => {
  if (likedMusic.value.length === 0) return '从未'

  // 找到最新的点赞时间
  const latestLike = likedMusic.value.reduce((latest, current) => {
    const currentTime = new Date(current.createdAt).getTime()
    const latestTime = new Date(latest.createdAt).getTime()
    return currentTime > latestTime ? current : latest
  })

  const date = new Date(latestLike.createdAt)
  const now = new Date()
  const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))

  if (diffInHours < 1) return '刚刚'
  if (diffInHours < 24) return `${diffInHours}小时前`

  const diffInDays = Math.floor(diffInHours / 24)
  if (diffInDays < 7) return `${diffInDays}天前`

  return date.toLocaleDateString('zh-CN')
}

// 页面挂载时获取数据
onMounted(async () => {
  console.log('页面挂载，开始检查用户状态...')
  console.log('当前用户状态:', user.value)

  // 如果用户信息不存在，尝试初始化认证状态
  if (!user.value) {
    console.log('用户信息不存在，尝试初始化认证状态...')
    try {
      await authStore.initAuth()
      console.log('认证状态初始化完成，用户信息:', authStore.user)
    } catch (error) {
      console.error('初始化认证状态失败:', error)
      return // 如果初始化失败，不继续执行
    }
  }

  // 等待一小段时间确保用户状态已更新
  await nextTick()

  console.log('准备获取喜欢的音乐，最终用户状态:', user.value)
  fetchLikedMusic()
})

definePageMeta({
  middleware: 'auth'
})

useHead({
  title: '我喜欢的音乐 - MusicDou',
  meta: [
    { name: 'description', content: '管理你收藏的音乐' }
  ]
})
</script>
