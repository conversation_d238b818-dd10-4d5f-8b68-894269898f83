<template>
  <div class="h-full bg-gray-50 dark:bg-gray-900 overflow-y-auto">
    <div class="max-w-6xl mx-auto px-6 py-6">
      <!-- 加载状态 -->
      <div v-if="loading" class="flex items-center justify-center py-16">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="text-center py-16">
        <Icon name="exclamation-triangle" class="w-16 h-16 text-red-500 mx-auto mb-4" />
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
          加载失败
        </h3>
        <p class="text-gray-500 dark:text-gray-400 mb-6">
          {{ error }}
        </p>
        <Button @click="fetchPlaylist">
          <Icon name="arrow-path" class="w-4 h-4 mr-2" />
          重试
        </Button>
      </div>

      <!-- 内容区域 -->
      <div v-else-if="playlist">
        <!-- 歌单头部 -->
        <div class="flex items-start gap-6 mb-8">
          <div class="w-32 h-32 sm:w-40 sm:h-40 lg:w-48 lg:h-48 rounded-lg overflow-hidden shadow-lg flex-shrink-0">
            <img
              v-if="playlist.coverUrl"
              :src="playlist.coverUrl"
              :alt="playlist.name"
              class="w-full h-full object-cover"
            />
            <div v-else class="w-full h-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center">
              <Icon name="musical-note" class="w-12 h-12 sm:w-16 sm:h-16 lg:w-24 lg:h-24 text-white" />
            </div>
          </div>
          <div class="flex-1 min-w-0">
            <h1 class="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 dark:text-white mb-2 truncate">
              {{ playlist.name || "获取失败"}}
            </h1>
            <p class="text-sm sm:text-base text-gray-600 dark:text-gray-400 mb-4 line-clamp-3">
              {{ playlist.description || "这个人很懒还没写描述~"}}
            </p>
            <div class="flex items-center gap-2 sm:gap-4 text-xs sm:text-sm text-gray-500 dark:text-gray-400 mb-4 sm:mb-6 flex-wrap">
              <span>{{ playlist.createdBy?.username || "无名"}}</span>
              <span>•</span>
              <span>{{ playlist.songCount || 0 }} 首歌曲</span>
              <span>•</span>
              <span class="hidden sm:inline">{{ formatDate(playlist.createdAt) }}</span>
            </div>
            <div class="flex items-center gap-2 sm:gap-3 flex-wrap mb-4">
              <Button
                size="lg"
                icon="play"
                icon-position="left"
                class="px-3 sm:px-4 lg:px-6 text-xs sm:text-sm lg:text-base"
                :disabled="!playlist.songCount"
                @click="handlePlayAll"
              >
                <span class="hidden sm:inline">播放全部</span>
                <span class="sm:hidden">全部</span>
              </Button>
              <Button
                size="lg"
                variant="outline"
                icon="arrow-path"
                icon-position="left"
                class="px-3 sm:px-4 lg:px-6 text-xs sm:text-sm lg:text-base"
                :disabled="!playlist.songCount"
                @click="handleShufflePlay"
              >
                <span class="hidden sm:inline">随机播放</span>
                <span class="sm:hidden">随机</span>
              </Button>
            </div>
          </div>
        </div>

        <!-- 歌曲列表 -->
        <Card>
          <div class="mb-4 flex items-center justify-between">
            <h2 class="text-lg font-semibold text-gray-900 dark:text-white">
              歌曲列表
            </h2>
            <div class="flex items-center gap-2 text-xs sm:text-sm text-gray-500 dark:text-gray-400">
              <span>{{ playlist.songs?.length || 0 }} 首歌曲</span>
            </div>
          </div>

          <div v-if="playlist.songs && playlist.songs.length > 0" class="space-y-2">
            <div
              v-for="(song, index) in playlist.songs"
              :key="song.id"
              class="flex items-center gap-4 p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors cursor-pointer group"
              @click="handlePlaySong(song, index)"
            >
              <div class="w-8 text-center">
                <span class="text-gray-500 dark:text-gray-400 group-hover:hidden">
                  {{ index + 1 }}
                </span>
                <Icon name="play" class="w-4 h-4 text-gray-600 dark:text-gray-400 hidden group-hover:block" />
              </div>
              <div class="w-12 h-12 rounded-lg overflow-hidden bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center">
                <img
                  v-if="song.coverUrl"
                  :src="song.coverUrl"
                  :alt="song.title"
                  class="w-full h-full object-cover"
                />
                <Icon v-else name="musical-note" class="w-6 h-6 text-white" />
              </div>
              <div class="flex-1 min-w-0">
                <h3 class="font-medium text-gray-900 dark:text-white truncate">
                  {{ song.title || '未知歌曲' }}
                </h3>
                <p class="text-sm text-gray-500 dark:text-gray-400 truncate">
                  {{ song.artist || '未知艺术家' }}
                </p>
              </div>
              <div class="text-sm text-gray-500 dark:text-gray-400 hidden md:block">
                {{ song.album || '未知专辑' }}
              </div>
              <div class="text-sm text-gray-500 dark:text-gray-400">
                {{ formatDuration(song.duration) }}
              </div>
              <div class="flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
                <Button
                  size="sm"
                  variant="ghost"
                  @click.stop="handleLikeSong(song)"
                >
                  <Icon
                    name="heart"
                    :class="[
                      'w-4 h-4',
                      song.isLiked ? 'text-red-500' : 'text-gray-400'
                    ]"
                  />
                </Button>
                <Button size="sm" variant="ghost" @click.stop="showSongMenu(song, $event)">
                  <Icon name="ellipsis-horizontal" class="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>

          <!-- 空状态 -->
          <div v-else class="text-center py-16">
            <Icon name="musical-note" class="w-16 h-16 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
              歌单还没有歌曲
            </h3>
            <p class="text-gray-500 dark:text-gray-400 mb-6">
              添加一些你喜欢的音乐到这个歌单吧
            </p>
          </div>
        </Card>
      </div>

      <!-- 404 状态 -->
      <div v-else class="flex flex-col items-center justify-center min-h-screen">
        <Icon name="exclamation-triangle" class="w-16 h-16 text-gray-400 mb-4" />
        <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">
          歌单不存在
        </h2>
        <p class="text-gray-600 dark:text-gray-400 mb-6">
          您访问的歌单可能已被删除或不存在
        </p>
        <Button @click="$router.push('/playlists')" variant="primary">
          返回歌单列表
        </Button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Playlist, Music } from '~/types'
import {useErrorHandler} from "~/composables/useErrorHandler";


// 路由参数
const route = useRoute()
const playlistId = route.params.id as string

// 响应式数据
const playlist = ref<Playlist | null>(null)
const loading = ref(true)
const error = ref<string | null>(null)

// Composables
const { getPlaylistById } = usePlaylistApi()
const { getMusicPlayUrl, recordPlay } = useMusicApi()
const { handleApiError } = useErrorHandler()
const { success: showSuccess, error: showError } = useNotification()

// 获取歌单详情
const fetchPlaylist = async () => {
  try {
    loading.value = true
    error.value = null
    
    const response = await getPlaylistById(playlistId)
    playlist.value = response.data
  } catch (err) {
    error.value = '获取歌单详情失败'
    handleApiError(err)
  } finally {
    loading.value = false
  }
}

// 格式化时长
const formatDuration = (seconds: number) => {
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
}

// 格式化日期
const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN')
}

// 播放单首歌曲
const handlePlaySong = async (song: Music, index: number) => {
  try {
    const { data: urlData } = await getMusicPlayUrl(song.id)
    const songWithUrl = { ...song, url: urlData.playUrl }
    
    await recordPlay(song.id)
    
    const audioPlayer = useAudioPlayer()
    await audioPlayer.playTrack(songWithUrl, [songWithUrl])
    
    showSuccess(`正在播放：${song.title}`)
  } catch (error) {
    handleError(error, '播放失败')
  }
}

// 播放全部
const handlePlayAll = async () => {
  if (!playlist.value?.songs?.length) return
  
  try {
    const songs = playlist.value.songs
    const audioPlayer = useAudioPlayer()
    
    const { data: urlData } = await getMusicPlayUrl(songs[0].id)
    const firstSongWithUrl = { ...songs[0], url: urlData.playUrl }
    
    await audioPlayer.playTrack(firstSongWithUrl, songs)
    
    showSuccess(`开始播放歌单：${playlist.value.name}`)
  } catch (error) {
    handleError(error, '播放失败')
  }
}

// 随机播放
const handleShufflePlay = async () => {
  if (!playlist.value?.songs?.length) return
  
  try {
    const songs = [...playlist.value.songs].sort(() => Math.random() - 0.5)
    const audioPlayer = useAudioPlayer()
    
    const { data: urlData } = await getMusicPlayUrl(songs[0].id)
    const firstSongWithUrl = { ...songs[0], url: urlData.playUrl }
    
    await audioPlayer.playTrack(firstSongWithUrl, songs)
    
    showSuccess(`随机播放歌单：${playlist.value.name}`)
  } catch (error) {
    handleError(error, '播放失败')
  }
}

// 喜欢歌曲
const handleLikeSong = async (song: Music) => {
  try {
    showSuccess(`已添加到我喜欢的音乐`)
  } catch (error) {
    handleError(error, '操作失败')
  }
}

// 显示歌曲菜单
const showSongMenu = (song: Music, event: Event) => {
  console.log('显示歌曲菜单', song)
}

// 组件挂载时获取数据
onMounted(() => {
  fetchPlaylist()
})
</script>
