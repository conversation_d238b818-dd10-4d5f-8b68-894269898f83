<template>
  <!-- 主页内容区域 -->
  <div class="h-full bg-gray-50 dark:bg-gray-900 overflow-y-auto">
      <!-- 个性化推荐横幅 -->
      <div class="bg-gradient-to-r from-red-500 to-pink-500 text-white p-6 mb-6">
        <div class="max-w-6xl mx-auto">
          <div class="flex items-center justify-between">
            <div>
              <h1 class="text-2xl font-bold mb-2">
                {{ getGreeting() }}，{{ user?.username || '音乐爱好者' }}
              </h1>
              <p class="text-red-100">
                {{ recommendationText }}
              </p>
            </div>

          </div>
        </div>
      </div>

      <div class="max-w-6xl mx-auto px-6 pb-6">
        <!-- 快速入口 -->
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
          <Card class="p-4 hover:shadow-md transition-all cursor-pointer hover:scale-105" @click="handleNavigate('/daily-recommend')">
            <div class="text-center">
              <div class="w-12 h-12 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center mx-auto mb-3">
                <Icon name="calendar-days" class="w-6 h-6 text-red-600 dark:text-red-400" />
              </div>
              <h3 class="font-medium text-gray-900 dark:text-white text-sm">每日推荐</h3>
            </div>
          </Card>

          <Card class="p-4 hover:shadow-md transition-all cursor-pointer hover:scale-105" @click="handleNavigate('/latest-uploads')">
            <div class="text-center">
              <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mx-auto mb-3">
                <Icon name="upload" class="w-6 h-6 text-blue-600 dark:text-blue-400" />
              </div>
              <h3 class="font-medium text-gray-900 dark:text-white text-sm">最新上传</h3>
            </div>
          </Card>

          <Card class="p-4 hover:shadow-md transition-all cursor-pointer hover:scale-105" @click="handleNavigate('/charts')">
            <div class="text-center">
              <div class="w-12 h-12 bg-yellow-100 dark:bg-yellow-900/30 rounded-full flex items-center justify-center mx-auto mb-3">
                <Icon name="trophy" class="w-6 h-6 text-yellow-600 dark:text-yellow-400" />
              </div>
              <h3 class="font-medium text-gray-900 dark:text-white text-sm">排行榜</h3>
            </div>
          </Card>

          <Card class="p-4 hover:shadow-md transition-all cursor-pointer hover:scale-105" @click="handleNavigate('/social/friends')">
            <div class="text-center">
              <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mx-auto mb-3">
                <Icon name="users" class="w-6 h-6 text-blue-600 dark:text-blue-400" />
              </div>
              <h3 class="font-medium text-gray-900 dark:text-white text-sm">朋友</h3>
            </div>
          </Card>
        </div>

        <!-- 推荐内容 -->
        <div class="space-y-8">
          <!-- 推荐歌单 -->
          <section>
            <div class="flex items-center justify-between mb-4">
              <h2 class="text-xl font-bold text-gray-900 dark:text-white">为你推荐</h2>
              <NuxtLink
                to="/playlists/discover"
                class="text-primary-600 hover:text-primary-700 dark:text-primary-400 text-sm font-medium"
              >
                查看更多 →
              </NuxtLink>
            </div>

            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              <PlaylistCard
                v-for="playlist in recommendedPlaylists"
                :key="playlist.id"
                :playlist="playlist"
                :show-actions="false"
                @play="handlePlayPlaylist"
              />
            </div>
          </section>

          <!-- 最近播放 -->
          <section v-if="recentTracks.length > 0">
            <div class="flex items-center justify-between mb-4">
              <h2 class="text-xl font-bold text-gray-900 dark:text-white">最近播放</h2>
            </div>

            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
              <MusicCard
                v-for="track in recentTracks"
                :key="track.id"
                :music="track"
                @play="handlePlayTrack"
                @like="handleLikeTrack"
              />
            </div>
          </section>
        </div>
      </div>
    </div>
</template>

<script setup lang="ts">
import type { Playlist, Music } from '~/types'

// 页面元数据
definePageMeta({
  middleware: 'auth' // 启用认证中间件
})

// 使用认证状态
const { user, isLoggedIn } = useAuth()

// 播放器状态
const playerStore = usePlayerStore()

// 响应式数据
const recommendedPlaylists = ref<Playlist[]>([])
const recentTracks = ref<Music[]>([])
const loading = ref(true)

// Composables
const { getRecommendedPlaylists } = usePlaylistApi()
const { getRecentTracks } = useMusicApi()
const { handleApiError, showError } = useErrorHandler()
const router = useRouter()

// 导航函数
const handleNavigate = (path: string) => {
  router.push(path)
}

// 获取问候语
const getGreeting = () => {
  const hour = new Date().getHours()
  if (hour < 6) return '夜深了'
  if (hour < 12) return '早上好'
  if (hour < 18) return '下午好'
  return '晚上好'
}

// 获取推荐文案
// 推荐文本 - 使用固定文本避免hydration不匹配
const recommendationText = ref('发现你可能喜欢的新音乐')

const getRecommendationText = () => {
  // 只在客户端更新随机文本，避免服务器端和客户端不匹配
  if (import.meta.client) {
    const texts = [
      '根据你的音乐品味，为你推荐这些歌曲',
      '发现你可能喜欢的新音乐',
      '基于你的听歌历史，精心为你挑选',
      '今天也要听好听的音乐哦'
    ] as const
    recommendationText.value = texts[Math.floor(Math.random() * texts.length)]
  }
  return recommendationText.value
}

// 获取推荐歌单
const fetchRecommendedPlaylists = async () => {
  try {
    const response = await getRecommendedPlaylists({ limit: 8 })
    recommendedPlaylists.value = response.data
  } catch (error) {
    const appError = handleApiError(error)
    showError(appError)
  }
}

// 获取最近播放
const fetchRecentTracks = async () => {
  try {
    const response = await getRecentTracks({ limit: 6 })
    recentTracks.value = response.data
  } catch (error) {
    const appError = handleApiError(error)
    showError(appError)
  }
}

// 播放歌单
const handlePlayPlaylist = (playlist: Playlist) => {
  if (playlist.songs?.length) {
    playerStore.setQueue(playlist.songs, 0)
    console.log(`开始播放歌单: ${playlist.name}`)
  } else {
    console.warn('歌单暂无歌曲')
  }
}

// 播放歌曲
const handlePlayTrack = (track: Music) => {
  playerStore.playTrack(track)
  console.log(`播放: ${track.title}`)
}

// 喜欢歌曲
const handleLikeTrack = (track: Music) => {
  // TODO: 实现喜欢歌曲功能
  console.log(`${track.isLiked ? '取消喜欢' : '喜欢'}: ${track.title}`)
}

// 页面加载时获取数据
onMounted(async () => {
  try {
    loading.value = true
    // 在客户端更新推荐文本
    getRecommendationText()
    await Promise.all([
      fetchRecommendedPlaylists(),
      fetchRecentTracks()
    ])
  } finally {
    loading.value = false
  }
})

// 页面元数据
useHead({
  title: 'MusicDou - 音乐主页',
  meta: [
    { name: 'description', content: '欢迎来到MusicDou，开始你的音乐之旅。' }
  ]
})
</script>
