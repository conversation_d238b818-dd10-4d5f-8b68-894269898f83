// 全局组件注册插件
export default defineNuxtPlugin((nuxtApp) => {
  // 手动注册UI组件以避免自动导入问题
  if (import.meta.client) {
    // 动态导入组件
    import('~/components/ui/Icon.vue').then((module) => {
      nuxtApp.vueApp.component('Icon', module.default)
    }).catch(() => {})

    import('~/components/ui/Button.vue').then((module) => {
      nuxtApp.vueApp.component('Button', module.default)
    }).catch(() => {})

    import('~/components/ui/Card.vue').then((module) => {
      nuxtApp.vueApp.component('Card', module.default)
    }).catch(() => {})

    import('~/components/ui/Input.vue').then((module) => {
      nuxtApp.vueApp.component('Input', module.default)
    }).catch(() => {})

    import('~/components/ui/Modal.vue').then((module) => {
      nuxtApp.vueApp.component('Modal', module.default)
    }).catch(() => {})

    import('~/components/ui/Loading.vue').then((module) => {
      nuxtApp.vueApp.component('Loading', module.default)
    }).catch(() => {})
  }
})
