const Playlist = require('../models/Playlist');
const PlaylistFavorite = require('../models/PlaylistFavorite');
const Music = require('../models/Music');
const mongoose = require('mongoose');

// 创建歌单
const createPlaylist = async (req, res) => {
  try {
    const { name, description, isPublic = true, tags = [], category = 'other' } = req.body;
    const userId = req.user.userId;
    
    // 验证必需字段
    if (!name || name.trim().length === 0) {
      return res.status(400).json({
        error: 'Validation Error',
        message: 'Playlist name is required'
      });
    }
    
    // 创建歌单
    const playlist = new Playlist({
      name: name.trim(),
      description: description ? description.trim() : '',
      isPublic,
      tags: Array.isArray(tags) ? tags.map(tag => tag.trim()).filter(tag => tag.length > 0) : [],
      category,
      createdBy: userId
    });
    
    await playlist.save();
    
    // 填充创建者信息
    await playlist.populate('createdBy', 'username avatar');
    
    res.status(201).json({
      success: true,
      message: 'Playlist created successfully',
      data: {
        playlist
      }
    });
    
  } catch (error) {
    console.error('Create playlist error:', error);
    
    if (error.name === 'ValidationError') {
      return res.status(400).json({
        error: 'Validation Error',
        message: Object.values(error.errors).map(err => err.message).join(', ')
      });
    }
    
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to create playlist'
    });
  }
};

// 获取歌单列表
const getPlaylists = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      sort = '-createdAt',
      category,
      tags,
      search,
      publicOnly = 'true',
      userId
    } = req.query;
    
    const pageNum = Math.max(1, parseInt(page));
    const limitNum = Math.min(50, Math.max(1, parseInt(limit)));
    
    let query = {};
    
    // 公开歌单过滤
    if (publicOnly === 'true') {
      query.isPublic = true;
    }
    
    // 用户歌单过滤
    if (userId) {
      query.createdBy = userId;
      // 如果查询特定用户的歌单，且不是当前用户，只显示公开歌单
      if (userId !== req.user?.userId) {
        query.isPublic = true;
      }
    }
    
    // 分类过滤
    if (category && category !== 'all') {
      query.category = category;
    }
    
    // 标签过滤
    if (tags) {
      const tagArray = Array.isArray(tags) ? tags : tags.split(',');
      query.tags = { $in: tagArray };
    }
    
    // 搜索过滤
    if (search && search.trim()) {
      query.$or = [
        { name: { $regex: search.trim(), $options: 'i' } },
        { description: { $regex: search.trim(), $options: 'i' } }
      ];
    }
    
    // 执行查询
    const playlists = await Playlist.find(query)
      .populate('createdBy', 'username avatar')
      .sort(sort)
      .skip((pageNum - 1) * limitNum)
      .limit(limitNum);
    
    // 获取总数
    const total = await Playlist.countDocuments(query);
    
    res.json({
      success: true,
      data: {
        playlists,
        pagination: {
          page: pageNum,
          limit: limitNum,
          total,
          pages: Math.ceil(total / limitNum)
        }
      }
    });
    
  } catch (error) {
    console.error('Get playlists error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to fetch playlists'
    });
  }
};

// 获取歌单详情
const getPlaylistById = async (req, res) => {
  try {
    const { id } = req.params;
    
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        error: 'Invalid ID',
        message: 'Invalid playlist ID format'
      });
    }
    
    const playlist = await Playlist.findById(id)
      .populate('createdBy', 'username avatar')
      .populate({
        path: 'songs.musicId',
        select: 'title artist album duration bitrate quality coverImage'
      });
    
    if (!playlist) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Playlist not found'
      });
    }
    
    // 检查权限：私有歌单只有创建者可以查看
    if (!playlist.isPublic && playlist.createdBy._id.toString() !== req.user?.userId) {
      return res.status(403).json({
        error: 'Access Denied',
        message: 'This playlist is private'
      });
    }
    
    // 检查当前用户是否收藏了这个歌单
    let isFavorited = false;
    if (req.user) {
      isFavorited = await PlaylistFavorite.exists({
        userId: req.user.userId,
        playlistId: id
      });
    }
    
    res.json({
      success: true,
      data: {
        ...playlist.toObject(),
        isFavorited: !!isFavorited
      }
    });
    
  } catch (error) {
    console.error('Get playlist error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to fetch playlist'
    });
  }
};

// 更新歌单
const updatePlaylist = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description, isPublic, tags, category } = req.body;
    const userId = req.user.userId;
    
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        error: 'Invalid ID',
        message: 'Invalid playlist ID format'
      });
    }
    
    const playlist = await Playlist.findById(id);
    
    if (!playlist) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Playlist not found'
      });
    }
    
    // 检查权限：只有创建者可以编辑
    if (playlist.createdBy.toString() !== userId.toString()) {
      return res.status(403).json({
        error: 'Access Denied',
        message: 'You can only edit your own playlists'
      });
    }
    
    // 不允许修改默认歌单的某些属性
    if (playlist.isDefault) {
      if (name && name !== playlist.name) {
        return res.status(400).json({
          error: 'Invalid Operation',
          message: 'Cannot change the name of default playlist'
        });
      }
    }
    
    // 更新字段
    if (name !== undefined) playlist.name = name.trim();
    if (description !== undefined) playlist.description = description.trim();
    if (isPublic !== undefined) playlist.isPublic = isPublic;
    if (category !== undefined) playlist.category = category;
    if (tags !== undefined) {
      playlist.tags = Array.isArray(tags) ? tags.map(tag => tag.trim()).filter(tag => tag.length > 0) : [];
    }
    
    await playlist.save();
    await playlist.populate('createdBy', 'username avatar');
    
    res.json({
      success: true,
      message: 'Playlist updated successfully',
      data: {
        playlist
      }
    });
    
  } catch (error) {
    console.error('Update playlist error:', error);
    
    if (error.name === 'ValidationError') {
      return res.status(400).json({
        error: 'Validation Error',
        message: Object.values(error.errors).map(err => err.message).join(', ')
      });
    }
    
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to update playlist'
    });
  }
};

// 删除歌单
const deletePlaylist = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.userId;

    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        error: 'Invalid ID',
        message: 'Invalid playlist ID format'
      });
    }

    const playlist = await Playlist.findById(id);

    if (!playlist) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Playlist not found'
      });
    }

    // 检查权限：只有创建者可以删除
    if (playlist.createdBy.toString() !== userId.toString()) {
      return res.status(403).json({
        error: 'Access Denied',
        message: 'You can only delete your own playlists'
      });
    }

    // 不允许删除默认歌单
    if (playlist.isDefault) {
      return res.status(400).json({
        error: 'Invalid Operation',
        message: 'Cannot delete default playlist'
      });
    }

    // 删除相关的收藏记录
    await PlaylistFavorite.deleteMany({ playlistId: id });

    // 删除歌单
    await Playlist.findByIdAndDelete(id);

    res.json({
      success: true,
      message: 'Playlist deleted successfully'
    });

  } catch (error) {
    console.error('Delete playlist error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to delete playlist'
    });
  }
};

// 获取用户的歌单
const getUserPlaylists = async (req, res) => {
  try {
    const { userId } = req.params;
    const { page = 1, limit = 20, includePrivate = 'false' } = req.query;

    const pageNum = Math.max(1, parseInt(page));
    const limitNum = Math.min(50, Math.max(1, parseInt(limit)));

    // 检查是否查看自己的歌单
    const isOwnPlaylists = userId === req.user?.userId;
    const shouldIncludePrivate = includePrivate === 'true' && isOwnPlaylists;

    const query = { createdBy: userId };
    if (!shouldIncludePrivate) {
      query.isPublic = true;
    }

    const playlists = await Playlist.find(query)
      .populate('createdBy', 'username avatar')
      .sort('-createdAt')
      .skip((pageNum - 1) * limitNum)
      .limit(limitNum);

    const total = await Playlist.countDocuments(query);

    res.json({
      success: true,
      data: {
        data: playlists,
        pagination: {
          page: pageNum,
          limit: limitNum,
          total,
          totalPages: Math.ceil(total / limitNum)
        }
      }
    });

  } catch (error) {
    console.error('Get user playlists error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to fetch user playlists'
    });
  }
};

// 获取热门歌单
const getPopularPlaylists = async (req, res) => {
  try {
    const { page = 1, limit = 20, timeRange = 'all' } = req.query;

    const pageNum = Math.max(1, parseInt(page));
    const limitNum = Math.min(50, Math.max(1, parseInt(limit)));

    const playlists = await Playlist.getPopularPlaylists({
      page: pageNum,
      limit: limitNum,
      timeRange
    });

    const total = await Playlist.countDocuments({ isPublic: true });

    res.json({
      success: true,
      data: {
        playlists,
        pagination: {
          page: pageNum,
          limit: limitNum,
          total,
          pages: Math.ceil(total / limitNum)
        }
      }
    });

  } catch (error) {
    console.error('Get popular playlists error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to fetch popular playlists'
    });
  }
};

// 添加歌曲到歌单
const addSongToPlaylist = async (req, res) => {
  try {
    const { id } = req.params;
    const { musicId, order } = req.body;
    const userId = req.user.userId;

    if (!mongoose.Types.ObjectId.isValid(id) || !mongoose.Types.ObjectId.isValid(musicId)) {
      return res.status(400).json({
        error: 'Invalid ID',
        message: 'Invalid playlist or music ID format'
      });
    }

    // 检查歌单是否存在
    const playlist = await Playlist.findById(id);
    if (!playlist) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Playlist not found'
      });
    }

    // 检查权限：只有创建者可以添加歌曲
    if (playlist.createdBy.toString() !== userId.toString()) {
      return res.status(403).json({
        error: 'Access Denied',
        message: 'You can only modify your own playlists'
      });
    }

    // 检查音乐是否存在
    const music = await Music.findById(musicId);
    if (!music) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Music not found'
      });
    }

    // 检查音乐是否已在歌单中
    const existingSong = playlist.songs.find(song => song.musicId.toString() === musicId);
    if (existingSong) {
      return res.status(400).json({
        error: 'Duplicate Song',
        message: 'Song already exists in playlist'
      });
    }

    // 添加歌曲
    const songOrder = order !== undefined ? order : playlist.songs.length;
    playlist.songs.push({
      musicId,
      order: songOrder,
      addedAt: new Date()
    });

    // 重新排序
    playlist.songs.sort((a, b) => a.order - b.order);

    await playlist.save();

    // 返回更新后的歌单
    await playlist.populate({
      path: 'songs.musicId',
      select: 'title artist album duration bitrate quality coverImage'
    });

    res.json({
      success: true,
      message: 'Song added to playlist successfully',
      data: {
        playlist,
        addedSong: music
      }
    });

  } catch (error) {
    console.error('Add song to playlist error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to add song to playlist'
    });
  }
};

// 从歌单移除歌曲
const removeSongFromPlaylist = async (req, res) => {
  try {
    const { id, musicId } = req.params;
    const userId = req.user.userId;

    if (!mongoose.Types.ObjectId.isValid(id) || !mongoose.Types.ObjectId.isValid(musicId)) {
      return res.status(400).json({
        error: 'Invalid ID',
        message: 'Invalid playlist or music ID format'
      });
    }

    // 检查歌单是否存在
    const playlist = await Playlist.findById(id);
    if (!playlist) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Playlist not found'
      });
    }

    // 检查权限：只有创建者可以移除歌曲
    if (playlist.createdBy.toString() !== userId.toString()) {
      return res.status(403).json({
        error: 'Access Denied',
        message: 'You can only modify your own playlists'
      });
    }

    // 查找并移除歌曲
    const songIndex = playlist.songs.findIndex(song => song.musicId.toString() === musicId);
    if (songIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Song not found in playlist'
      });
    }

    const removedSong = playlist.songs[songIndex];
    playlist.songs.splice(songIndex, 1);

    // 重新排序
    playlist.songs.forEach((song, index) => {
      song.order = index;
    });

    await playlist.save();

    res.json({
      success: true,
      message: 'Song removed from playlist successfully',
      data: {
        playlist,
        removedSong
      }
    });

  } catch (error) {
    console.error('Remove song from playlist error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to remove song from playlist'
    });
  }
};

// 重新排序歌单中的歌曲
const reorderPlaylistSongs = async (req, res) => {
  try {
    const { id } = req.params;
    const { songOrders } = req.body; // [{ musicId, order }, ...]
    const userId = req.user.userId;

    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        error: 'Invalid ID',
        message: 'Invalid playlist ID format'
      });
    }

    if (!Array.isArray(songOrders)) {
      return res.status(400).json({
        error: 'Invalid Data',
        message: 'songOrders must be an array'
      });
    }

    // 检查歌单是否存在
    const playlist = await Playlist.findById(id);
    if (!playlist) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Playlist not found'
      });
    }

    // 检查权限：只有创建者可以重新排序
    if (playlist.createdBy.toString() !== userId.toString()) {
      return res.status(403).json({
        error: 'Access Denied',
        message: 'You can only modify your own playlists'
      });
    }

    // 更新歌曲顺序
    songOrders.forEach(({ musicId, order }) => {
      const song = playlist.songs.find(s => s.musicId.toString() === musicId.toString());
      if (song) {
        song.order = order;
      }
    });

    // 重新排序
    playlist.songs.sort((a, b) => a.order - b.order);

    await playlist.save();

    // 返回更新后的歌单
    await playlist.populate({
      path: 'songs.musicId',
      select: 'title artist album duration bitrate quality coverImage'
    });

    res.json({
      success: true,
      message: 'Playlist songs reordered successfully',
      data: {
        playlist
      }
    });

  } catch (error) {
    console.error('Reorder playlist songs error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to reorder playlist songs'
    });
  }
};

// 批量添加歌曲到歌单
const batchAddSongsToPlaylist = async (req, res) => {
  try {
    const { id } = req.params;
    const { musicIds } = req.body; // [musicId1, musicId2, ...]
    const userId = req.user.userId;

    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        error: 'Invalid ID',
        message: 'Invalid playlist ID format'
      });
    }

    if (!Array.isArray(musicIds) || musicIds.length === 0) {
      return res.status(400).json({
        error: 'Invalid Data',
        message: 'musicIds must be a non-empty array'
      });
    }

    // 验证所有音乐ID格式
    const invalidIds = musicIds.filter(id => !mongoose.Types.ObjectId.isValid(id));
    if (invalidIds.length > 0) {
      return res.status(400).json({
        error: 'Invalid ID',
        message: `Invalid music IDs: ${invalidIds.join(', ')}`
      });
    }

    // 检查歌单是否存在
    const playlist = await Playlist.findById(id);
    if (!playlist) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Playlist not found'
      });
    }

    // 检查权限：只有创建者可以添加歌曲
    if (playlist.createdBy.toString() !== userId.toString()) {
      return res.status(403).json({
        error: 'Access Denied',
        message: 'You can only modify your own playlists'
      });
    }

    // 检查音乐是否存在
    const existingMusic = await Music.find({ _id: { $in: musicIds } });
    const existingMusicIds = existingMusic.map(m => m._id.toString());
    const notFoundIds = musicIds.filter(id => !existingMusicIds.includes(id.toString()));

    if (notFoundIds.length > 0) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Music not found: ${notFoundIds.join(', ')}`
      });
    }

    // 过滤已存在的歌曲
    const existingSongIds = playlist.songs.map(song => song.musicId.toString());
    const newMusicIds = musicIds.filter(id => !existingSongIds.includes(id.toString()));

    if (newMusicIds.length === 0) {
      return res.status(400).json({
        error: 'Duplicate Songs',
        message: 'All songs already exist in playlist'
      });
    }

    // 批量添加歌曲
    let currentOrder = playlist.songs.length;
    const newSongs = newMusicIds.map(musicId => ({
      musicId,
      order: currentOrder++,
      addedAt: new Date()
    }));

    playlist.songs.push(...newSongs);
    await playlist.save();

    res.json({
      success: true,
      message: `${newSongs.length} songs added to playlist successfully`,
      data: {
        playlist,
        addedCount: newSongs.length,
        skippedCount: musicIds.length - newSongs.length
      }
    });

  } catch (error) {
    console.error('Batch add songs to playlist error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to add songs to playlist'
    });
  }
};

// 收藏歌单
const favoritePlaylist = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.userId;

    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        error: 'Invalid ID',
        message: 'Invalid playlist ID format'
      });
    }

    // 检查歌单是否存在
    const playlist = await Playlist.findById(id);
    if (!playlist) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Playlist not found'
      });
    }

    // 不能收藏自己的歌单
    if (playlist.createdBy.toString() === userId.toString()) {
      return res.status(400).json({
        error: 'Invalid Operation',
        message: 'You cannot favorite your own playlist'
      });
    }

    // 只能收藏公开歌单
    if (!playlist.isPublic) {
      return res.status(403).json({
        error: 'Access Denied',
        message: 'Cannot favorite private playlist'
      });
    }

    // 添加收藏
    try {
      const favorite = await PlaylistFavorite.addFavorite(userId, id);

      res.json({
        success: true,
        message: 'Playlist favorited successfully',
        data: {
          favorite,
          playlist: {
            _id: playlist._id,
            name: playlist.name,
            favoriteCount: playlist.favoriteCount + 1
          }
        }
      });

    } catch (error) {
      if (error.message === 'Playlist already favorited') {
        return res.status(400).json({
          error: 'Already Favorited',
          message: 'You have already favorited this playlist'
        });
      }
      throw error;
    }

  } catch (error) {
    console.error('Favorite playlist error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to favorite playlist'
    });
  }
};

// 取消收藏歌单
const unfavoritePlaylist = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.userId;

    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        error: 'Invalid ID',
        message: 'Invalid playlist ID format'
      });
    }

    // 取消收藏
    const favorite = await PlaylistFavorite.removeFavorite(userId, id);

    if (!favorite) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Playlist not in your favorites'
      });
    }

    // 获取更新后的歌单信息
    const playlist = await Playlist.findById(id);

    res.json({
      success: true,
      message: 'Playlist unfavorited successfully',
      data: {
        playlist: {
          _id: playlist._id,
          name: playlist.name,
          favoriteCount: playlist.favoriteCount
        }
      }
    });

  } catch (error) {
    console.error('Unfavorite playlist error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to unfavorite playlist'
    });
  }
};

// 获取用户收藏的歌单
const getUserFavorites = async (req, res) => {
  try {
    const { page = 1, limit = 20 } = req.query;
    const userId = req.user.userId;

    const pageNum = Math.max(1, parseInt(page));
    const limitNum = Math.min(50, Math.max(1, parseInt(limit)));

    // 获取收藏的歌单
    const favorites = await PlaylistFavorite.getUserFavorites(userId, {
      page: pageNum,
      limit: limitNum
    });

    // 获取总数
    const total = await PlaylistFavorite.countDocuments({ userId });

    // 格式化响应数据
    const playlists = favorites.map(favorite => ({
      ...favorite.playlistId.toObject(),
      favoritedAt: favorite.createdAt,
      isFavorited: true
    }));

    res.json({
      success: true,
      data: {
        data: playlists,
        pagination: {
          page: pageNum,
          limit: limitNum,
          total,
          totalPages: Math.ceil(total / limitNum)
        }
      }
    });

  } catch (error) {
    console.error('Get user favorites error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to fetch favorite playlists'
    });
  }
};

// 上传歌单封面
const uploadPlaylistCover = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.userId;

    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        error: 'Invalid ID',
        message: 'Invalid playlist ID format'
      });
    }

    // 检查歌单是否存在
    const playlist = await Playlist.findById(id);
    if (!playlist) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Playlist not found'
      });
    }

    // 检查权限：只有创建者可以上传封面
    if (playlist.createdBy.toString() !== userId.toString()) {
      return res.status(403).json({
        error: 'Access Denied',
        message: 'You can only modify your own playlists'
      });
    }

    // 检查是否有上传的文件
    if (!req.uploadResult) {
      return res.status(400).json({
        error: 'No File',
        message: 'No cover image uploaded'
      });
    }

    // 更新歌单封面
    playlist.coverImage = req.uploadResult.objectName;
    await playlist.save();

    // 生成封面访问URL
    const coverUrl = `http://localhost:9000/${req.uploadResult.bucket}/${req.uploadResult.objectName}`;

    res.json({
      success: true,
      message: 'Playlist cover uploaded successfully',
      data: {
        playlist: {
          _id: playlist._id,
          name: playlist.name,
          coverImage: playlist.coverImage,
          coverUrl
        },
        uploadResult: req.uploadResult
      }
    });

  } catch (error) {
    console.error('Upload playlist cover error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to upload playlist cover'
    });
  }
};

// 删除歌单封面
const removePlaylistCover = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.userId;

    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        error: 'Invalid ID',
        message: 'Invalid playlist ID format'
      });
    }

    // 检查歌单是否存在
    const playlist = await Playlist.findById(id);
    if (!playlist) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Playlist not found'
      });
    }

    // 检查权限：只有创建者可以删除封面
    if (playlist.createdBy.toString() !== userId.toString()) {
      return res.status(403).json({
        error: 'Access Denied',
        message: 'You can only modify your own playlists'
      });
    }

    // 删除封面
    const oldCoverImage = playlist.coverImage;
    playlist.coverImage = null;
    await playlist.save();

    res.json({
      success: true,
      message: 'Playlist cover removed successfully',
      data: {
        playlist: {
          _id: playlist._id,
          name: playlist.name,
          coverImage: playlist.coverImage
        },
        removedCover: oldCoverImage
      }
    });

  } catch (error) {
    console.error('Remove playlist cover error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to remove playlist cover'
    });
  }
};

module.exports = {
  createPlaylist,
  getPlaylists,
  getPlaylistById,
  updatePlaylist,
  deletePlaylist,
  getUserPlaylists,
  getPopularPlaylists,
  addSongToPlaylist,
  removeSongFromPlaylist,
  reorderPlaylistSongs,
  batchAddSongsToPlaylist,
  favoritePlaylist,
  unfavoritePlaylist,
  getUserFavorites,
  uploadPlaylistCover,
  removePlaylistCover
};
